# .so File Analysis Checklist

## Critical Information to Extract

### 1. API Endpoints and URLs
- [ ] Base URLs for phone lookup services
- [ ] API endpoint patterns
- [ ] Authentication endpoints
- [ ] Configuration URLs
- [ ] Backup/fallback servers

### 2. Authentication Mechanisms
- [ ] API keys (hardcoded or obfuscated)
- [ ] Authentication tokens
- [ ] Certificate pinning
- [ ] Custom authentication schemes
- [ ] OAuth implementations

### 3. Phone Number Processing
- [ ] Number formatting functions
- [ ] Validation algorithms
- [ ] Country code handling
- [ ] Number normalization
- [ ] Regex patterns for phone numbers

### 4. Network Communication
- [ ] HTTP client implementations
- [ ] Request/response structures
- [ ] Encryption methods
- [ ] Protocol specifications
- [ ] Error handling mechanisms

### 5. Anti-Analysis Features
- [ ] Debugger detection
- [ ] Emulator detection
- [ ] Root detection
- [ ] Integrity checks
- [ ] Obfuscation techniques

### 6. Data Structures
- [ ] Contact information structures
- [ ] API response formats
- [ ] Configuration objects
- [ ] Cache structures
- [ ] Database schemas

## Analysis Targets by File

### libtoolChecker.so
**Primary Focus**: Security and anti-analysis
- [ ] Anti-debug functions
- [ ] Environment detection
- [ ] Integrity verification
- [ ] Obfuscation routines
- [ ] License/activation checks

### libapp.so  
**Primary Focus**: Core functionality
- [ ] JNI bridge functions
- [ ] Phone lookup algorithms
- [ ] API communication
- [ ] Data processing
- [ ] Business logic

### libflutter.so
**Primary Focus**: Framework integration
- [ ] Platform channels
- [ ] Native method bridges
- [ ] Custom implementations
- [ ] Performance optimizations

## Extraction Techniques

### 1. String Extraction
```bash
# Extract all strings and categorize
strings libapp.so > strings_libapp.txt
grep -i -E "(http|api|phone|contact)" strings_libapp.txt > api_strings.txt
grep -i -E "(key|token|auth|secret)" strings_libapp.txt > auth_strings.txt
```

### 2. Function Mapping
```bash
# Extract function symbols
nm -D libapp.so > functions_libapp.txt
objdump -T libapp.so > dynamic_symbols.txt
```

### 3. Cross-Reference Analysis
- Map string usage to functions
- Trace API call flows
- Identify data transformation points
- Document encryption/decryption routines

## Documentation Template

### For Each Discovered API:
```
API Name: [Name/Purpose]
Endpoint: [Full URL]
Method: [GET/POST/etc]
Authentication: [Type and details]
Parameters: [Request structure]
Response: [Response format]
Location: [Function/Address in binary]
Notes: [Additional observations]
```

### For Each Function:
```
Function Name: [Symbol name]
Address: [Memory address]
Purpose: [What it does]
Parameters: [Input parameters]
Return Value: [Output type]
Calls: [Other functions it calls]
Called By: [Functions that call it]
Notes: [Interesting observations]
```

## Advanced Analysis Goals

### 1. Reconstruct API Protocol
- [ ] Complete request/response flow
- [ ] Authentication sequence
- [ ] Error handling
- [ ] Rate limiting mechanisms

### 2. Identify Vulnerabilities
- [ ] Hardcoded credentials
- [ ] Weak encryption
- [ ] Input validation issues
- [ ] Logic flaws

### 3. Bypass Anti-Analysis
- [ ] Identify detection mechanisms
- [ ] Find bypass techniques
- [ ] Document evasion methods

### 4. Create Attack Vectors
- [ ] API abuse possibilities
- [ ] Data extraction methods
- [ ] Privilege escalation
- [ ] Reverse engineering countermeasures
