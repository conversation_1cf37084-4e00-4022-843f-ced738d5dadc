// Enhanced Transport System Monitor - Better endpoint and API call extraction
// Targets Google's CCT transport and captures actual HTTP requests/responses

var logFile = null;
var capturedEndpoints = new Set();
var capturedApiKeys = new Set();

function initTransportLogging() {
    try {
        logFile = new File("/data/data/app.menodag.spamkiller/enhanced_transport.log", "w");
        console.log("[+] Enhanced transport monitor initialized");
    } catch (e) {
        try {
            logFile = new File("/sdcard/enhanced_transport.log", "w");
            console.log("[+] Enhanced transport monitor initialized: /sdcard/");
        } catch (e2) {
            console.log("[-] Failed to initialize log: " + e2.message);
        }
    }
}

function logTransport(category, details) {
    var timestamp = new Date().toISOString();
    var logEntry = timestamp + " [ENHANCED_" + category + "] " + JSON.stringify(details) + "\n";
    
    console.log("[ENHANCED] " + category + ": " + JSON.stringify(details, null, 2));
    
    if (logFile) {
        try {
            logFile.write(logEntry);
            logFile.flush();
        } catch (e) {}
    }
}

function extractDomain(url) {
    try {
        var urlObj = new URL(url);
        return urlObj.getHost();
    } catch (e) {
        return "unknown";
    }
}

function isPhoneRelated(str) {
    if (!str) return false;
    var lowerStr = str.toLowerCase();
    return lowerStr.includes("phone") || lowerStr.includes("caller") ||
           lowerStr.includes("lookup") || lowerStr.includes("contact") ||
           lowerStr.includes("msisdn") || lowerStr.includes("subscriber") ||
           lowerStr.includes("number") || lowerStr.includes("name");
}

Java.perform(function() {
    initTransportLogging();
    
    // ============= ENHANCED HTTP URL CONNECTION MONITORING =============
    try {
        var HttpURLConnection = Java.use("java.net.HttpURLConnection");
        
        // Hook openConnection to capture all URL connections
        var URL = Java.use("java.net.URL");
        URL.openConnection.overload().implementation = function() {
            var connection = this.openConnection();
            var urlStr = this.toString();
            
            if (urlStr && !urlStr.includes("127.0.0.1") && !urlStr.includes("localhost")) {
                capturedEndpoints.add(urlStr);
                
                logTransport("HTTP_CONNECTION", {
                    url: urlStr,
                    domain: extractDomain(urlStr),
                    isPhoneRelated: isPhoneRelated(urlStr),
                    method: "URL.openConnection"
                });
            }
            
            return connection;
        };
        
        // Hook setRequestProperty for headers and API keys
        HttpURLConnection.setRequestProperty.implementation = function(key, value) {
            var urlStr = this.getURL() ? this.getURL().toString() : "unknown";
            
            if (key && value) {
                // Capture API keys and important headers
                if (key.toLowerCase().includes("api") || key.toLowerCase().includes("auth") ||
                    key.toLowerCase().includes("key") || key.toLowerCase().includes("token")) {
                    
                    capturedApiKeys.add(value);
                    
                    logTransport("API_KEY", {
                        header: key,
                        value: value,
                        url: urlStr,
                        domain: extractDomain(urlStr),
                        method: "HttpURLConnection.setRequestProperty"
                    });
                }
                
                // Log all headers for phone-related requests
                if (isPhoneRelated(urlStr) || isPhoneRelated(key) || isPhoneRelated(value)) {
                    logTransport("HTTP_HEADER", {
                        header: key,
                        value: value,
                        url: urlStr,
                        domain: extractDomain(urlStr),
                        method: "HttpURLConnection.setRequestProperty"
                    });
                }
            }
            
            return this.setRequestProperty(key, value);
        };
        
        // Hook getOutputStream to capture request data
        HttpURLConnection.getOutputStream.implementation = function() {
            var outputStream = this.getOutputStream();
            var urlStr = this.getURL() ? this.getURL().toString() : "unknown";
            
            logTransport("HTTP_REQUEST", {
                url: urlStr,
                domain: extractDomain(urlStr),
                method: this.getRequestMethod(),
                contentType: this.getRequestProperty("Content-Type"),
                userAgent: this.getRequestProperty("User-Agent"),
                timestamp: new Date().toISOString()
            });
            
            return outputStream;
        };
        
        // Hook getInputStream to capture response data
        HttpURLConnection.getInputStream.implementation = function() {
            var inputStream = this.getInputStream();
            var urlStr = this.getURL() ? this.getURL().toString() : "unknown";
            
            logTransport("HTTP_RESPONSE", {
                url: urlStr,
                domain: extractDomain(urlStr),
                responseCode: this.getResponseCode(),
                contentType: this.getHeaderField("Content-Type"),
                contentLength: this.getHeaderField("Content-Length"),
                timestamp: new Date().toISOString()
            });
            
            return inputStream;
        };
        
        console.log("[+] Enhanced HttpURLConnection monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook HttpURLConnection: " + e.message);
    }
    
    // ============= OKHTTP MONITORING (if present) =============
    try {
        var OkHttpClient = Java.use("okhttp3.OkHttpClient");
        var Request = Java.use("okhttp3.Request");
        
        // Hook OkHttp requests
        OkHttpClient.newCall.implementation = function(request) {
            var call = this.newCall(request);
            var url = request.url().toString();
            
            if (url && !url.includes("127.0.0.1")) {
                logTransport("OKHTTP_REQUEST", {
                    url: url,
                    domain: extractDomain(url),
                    method: request.method(),
                    headers: request.headers().toString(),
                    isPhoneRelated: isPhoneRelated(url)
                });
            }
            
            return call;
        };
        
        console.log("[+] OkHttp monitoring active");
    } catch (e) {
        console.log("[-] OkHttp not found or failed to hook: " + e.message);
    }
    
    // ============= RETROFIT MONITORING (if present) =============
    try {
        var Retrofit = Java.use("retrofit2.Retrofit");
        
        Retrofit.create.implementation = function(serviceClass) {
            var service = this.create(serviceClass);
            var baseUrl = this.baseUrl().toString();
            
            logTransport("RETROFIT_SERVICE", {
                baseUrl: baseUrl,
                serviceClass: serviceClass.getName(),
                domain: extractDomain(baseUrl)
            });
            
            return service;
        };
        
        console.log("[+] Retrofit monitoring active");
    } catch (e) {
        console.log("[-] Retrofit not found or failed to hook: " + e.message);
    }
    
    // ============= SOCKET CONNECTION MONITORING =============
    try {
        var Socket = Java.use("java.net.Socket");
        
        Socket.connect.overload('java.net.SocketAddress', 'int').implementation = function(endpoint, timeout) {
            if (endpoint) {
                logTransport("SOCKET_CONNECTION", {
                    endpoint: endpoint.toString(),
                    timeout: timeout,
                    method: "Socket.connect"
                });
            }
            
            return this.connect(endpoint, timeout);
        };
        
        console.log("[+] Socket monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook Socket: " + e.message);
    }
    
    // ============= DNS RESOLUTION MONITORING =============
    try {
        var InetAddress = Java.use("java.net.InetAddress");
        
        InetAddress.getByName.implementation = function(host) {
            var result = this.getByName(host);
            
            if (host && !host.includes("127.0.0.1") && !host.includes("localhost")) {
                logTransport("DNS_LOOKUP", {
                    hostname: host,
                    resolvedIp: result.getHostAddress(),
                    isPhoneRelated: isPhoneRelated(host)
                });
            }
            
            return result;
        };
        
        console.log("[+] DNS monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook DNS: " + e.message);
    }
    
    // ============= SUMMARY REPORTING =============
    setTimeout(function() {
        console.log("\n[SUMMARY] Captured Endpoints:");
        capturedEndpoints.forEach(function(endpoint) {
            console.log("  - " + endpoint);
        });
        
        console.log("\n[SUMMARY] Captured API Keys:");
        capturedApiKeys.forEach(function(key) {
            console.log("  - " + key.substring(0, 20) + "...");
        });
    }, 10000); // Report after 10 seconds
    
    console.log("[+] Enhanced transport monitoring active");
    console.log("[+] Monitoring: HTTP, OkHttp, Retrofit, Sockets, DNS");
    console.log("[+] Focus: Endpoint extraction and API key capture");
});
