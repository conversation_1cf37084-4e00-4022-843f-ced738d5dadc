// Flutter/WebView API Monitor - Targets Flutter apps with WebView-based phone lookup
// Based on analysis showing this is a Flutter app using WebViews for core functionality

Java.perform(function() {
    console.log("[+] Flutter/WebView API Monitor starting...");
    
    var discoveredApis = [];
    var webviewUrls = [];
    var jsExecutions = [];
    var phoneRelatedData = [];
    
    function isPhoneRelated(str) {
        if (!str) return false;
        var lowerStr = str.toLowerCase();
        return lowerStr.includes("phone") || lowerStr.includes("caller") ||
               lowerStr.includes("lookup") || lowerStr.includes("contact") ||
               lowerStr.includes("msisdn") || lowerStr.includes("number") ||
               lowerStr.includes("name") || lowerStr.includes("search") ||
               lowerStr.includes("identify") || lowerStr.includes("spam") ||
               lowerStr.includes("block") || lowerStr.includes("truecaller") ||
               lowerStr.includes("getcontact") || lowerStr.includes("api");
    }
    
    function logDiscovery(category, data) {
        var timestamp = new Date().toISOString();
        console.log("[" + category + "] " + JSON.stringify(data, null, 2));
        
        if (category === "API_CALL") {
            discoveredApis.push({...data, timestamp});
        } else if (category === "WEBVIEW_URL") {
            webviewUrls.push({...data, timestamp});
        } else if (category === "JS_EXECUTION") {
            jsExecutions.push({...data, timestamp});
        }
        
        if (isPhoneRelated(JSON.stringify(data))) {
            phoneRelatedData.push({category, data, timestamp});
        }
    }
    
    // ============= WEBVIEW URL LOADING MONITORING =============
    try {
        var WebView = Java.use("android.webkit.WebView");
        
        // Hook loadUrl - primary method for loading URLs
        WebView.loadUrl.overload('java.lang.String').implementation = function(url) {
            if (url && !url.includes("data:") && !url.includes("about:")) {
                logDiscovery("WEBVIEW_URL", {
                    url: url,
                    method: "loadUrl",
                    isPhoneRelated: isPhoneRelated(url)
                });
            }
            return this.loadUrl(url);
        };
        
        // Hook loadUrl with headers
        WebView.loadUrl.overload('java.lang.String', 'java.util.Map').implementation = function(url, headers) {
            if (url && !url.includes("data:") && !url.includes("about:")) {
                var headerStr = headers ? headers.toString() : "";
                logDiscovery("WEBVIEW_URL", {
                    url: url,
                    headers: headerStr,
                    method: "loadUrl_with_headers",
                    isPhoneRelated: isPhoneRelated(url) || isPhoneRelated(headerStr)
                });
            }
            return this.loadUrl(url, headers);
        };
        
        console.log("[+] WebView URL loading monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook WebView: " + e.message);
    }
    
    // ============= WEBVIEW CLIENT URL OVERRIDE MONITORING =============
    try {
        var WebViewClient = Java.use("android.webkit.WebViewClient");
        
        // Hook shouldOverrideUrlLoading - captures URL redirects and API calls
        WebViewClient.shouldOverrideUrlLoading.overload('android.webkit.WebView', 'java.lang.String').implementation = function(webView, url) {
            if (url && !url.includes("data:") && !url.includes("about:")) {
                logDiscovery("WEBVIEW_OVERRIDE", {
                    url: url,
                    method: "shouldOverrideUrlLoading",
                    isPhoneRelated: isPhoneRelated(url)
                });
            }
            return this.shouldOverrideUrlLoading(webView, url);
        };
        
        // Hook onLoadResource - captures all resource loading
        WebViewClient.onLoadResource.implementation = function(webView, url) {
            if (url && !url.includes("data:") && !url.includes("about:") && 
                !url.includes(".css") && !url.includes(".js") && !url.includes(".png") && 
                !url.includes(".jpg") && !url.includes(".gif")) {
                
                logDiscovery("WEBVIEW_RESOURCE", {
                    url: url,
                    method: "onLoadResource",
                    isPhoneRelated: isPhoneRelated(url)
                });
            }
            return this.onLoadResource(webView, url);
        };
        
        console.log("[+] WebViewClient monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook WebViewClient: " + e.message);
    }
    
    // ============= JAVASCRIPT EXECUTION MONITORING =============
    try {
        var WebView = Java.use("android.webkit.WebView");
        
        // Hook evaluateJavascript - captures JavaScript API calls
        WebView.evaluateJavascript.implementation = function(script, resultCallback) {
            if (script && (script.includes("http") || script.includes("fetch") || 
                          script.includes("XMLHttpRequest") || script.includes("ajax") ||
                          isPhoneRelated(script))) {
                
                logDiscovery("JS_EXECUTION", {
                    script: script.length > 200 ? script.substring(0, 200) + "..." : script,
                    hasCallback: resultCallback !== null,
                    method: "evaluateJavascript",
                    isPhoneRelated: isPhoneRelated(script)
                });
            }
            return this.evaluateJavascript(script, resultCallback);
        };
        
        console.log("[+] JavaScript execution monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook JavaScript execution: " + e.message);
    }
    
    // ============= FLUTTER HTTP CHANNEL MONITORING =============
    try {
        // Try to hook Flutter's method channel for HTTP requests
        var MethodChannel = Java.use("io.flutter.plugin.common.MethodChannel");
        
        MethodChannel.invokeMethod.overload('java.lang.String', 'java.lang.Object').implementation = function(method, arguments) {
            if (method && (method.includes("http") || method.includes("request") || 
                          method.includes("fetch") || isPhoneRelated(method))) {
                
                logDiscovery("FLUTTER_METHOD", {
                    method: method,
                    arguments: arguments ? arguments.toString() : null,
                    isPhoneRelated: isPhoneRelated(method) || isPhoneRelated(arguments ? arguments.toString() : "")
                });
            }
            return this.invokeMethod(method, arguments);
        };
        
        console.log("[+] Flutter method channel monitoring active");
    } catch (e) {
        console.log("[-] Flutter method channel not found: " + e.message);
    }
    
    // ============= INTENT DATA MONITORING =============
    try {
        var Intent = Java.use("android.content.Intent");
        
        // Hook setData for Intent URLs
        Intent.setData.implementation = function(data) {
            if (data && data.toString().includes("http")) {
                logDiscovery("INTENT_DATA", {
                    data: data.toString(),
                    method: "Intent.setData",
                    isPhoneRelated: isPhoneRelated(data.toString())
                });
            }
            return this.setData(data);
        };
        
        console.log("[+] Intent data monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook Intent: " + e.message);
    }
    
    // ============= PERIODIC REPORTING =============
    var reportCount = 0;
    var reportInterval = setInterval(function() {
        reportCount++;
        
        console.log("\n" + "=".repeat(80));
        console.log("FLUTTER/WEBVIEW API MONITOR REPORT #" + reportCount);
        console.log("=".repeat(80));
        
        if (webviewUrls.length > 0) {
            console.log("\n[WEBVIEW URLS] (" + webviewUrls.length + " total):");
            webviewUrls.slice(-5).forEach(function(item, index) {
                console.log("  " + (index + 1) + ". " + item.url);
                console.log("     Method: " + item.method);
                console.log("     Phone Related: " + item.isPhoneRelated);
                console.log("     Time: " + item.timestamp);
                console.log("");
            });
        }
        
        if (jsExecutions.length > 0) {
            console.log("\n[JAVASCRIPT EXECUTIONS] (" + jsExecutions.length + " total):");
            jsExecutions.slice(-3).forEach(function(item, index) {
                console.log("  " + (index + 1) + ". " + item.script);
                console.log("     Phone Related: " + item.isPhoneRelated);
                console.log("     Time: " + item.timestamp);
                console.log("");
            });
        }
        
        if (phoneRelatedData.length > 0) {
            console.log("\n[PHONE-RELATED DISCOVERIES] (" + phoneRelatedData.length + " total):");
            phoneRelatedData.slice(-5).forEach(function(item, index) {
                console.log("  " + (index + 1) + ". [" + item.category + "] " + JSON.stringify(item.data));
                console.log("     Time: " + item.timestamp);
                console.log("");
            });
        }
        
        if (discoveredApis.length === 0 && webviewUrls.length === 0 && jsExecutions.length === 0) {
            console.log("\n[NO API CALLS DETECTED YET]");
            console.log("Try using the phone lookup features in the app");
            console.log("Make sure to:");
            console.log("- Search for phone numbers");
            console.log("- Use caller ID features");
            console.log("- Check spam/block functionality");
        }
        
        console.log("=".repeat(80));
        
        // Stop after 10 reports
        if (reportCount >= 10) {
            clearInterval(reportInterval);
            console.log("[+] Flutter/WebView API monitoring completed");
        }
        
    }, 10000); // Report every 10 seconds
    
    console.log("[+] Flutter/WebView API Monitor active");
    console.log("[+] Monitoring: WebView URLs, JavaScript execution, Flutter channels");
    console.log("[+] Focus: Phone lookup API calls in WebViews and Flutter code");
    console.log("[+] Will report every 10 seconds for ~2 minutes");
});
