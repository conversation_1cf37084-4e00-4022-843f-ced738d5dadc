# q3.m0
package q3;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteDatabaseLockedException;
import android.os.SystemClock;
import android.util.Base64;
import i3.i;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import l3.a;
import l3.c;
import r3.b;

/* loaded from: classes.dex */
public class m0 implements q3.d, r3.b, q3.c {

    /* renamed from: l, reason: collision with root package name */
    private static final g3.b f17560l = g3.b.b("proto");

    /* renamed from: a, reason: collision with root package name */
    private final t0 f17561a;

    /* renamed from: b, reason: collision with root package name */
    private final s3.a f17562b;

    /* renamed from: c, reason: collision with root package name */
    private final s3.a f17563c;

    /* renamed from: d, reason: collision with root package name */
    private final e f17564d;

    /* renamed from: e, reason: collision with root package name */
    private final rc.a<String> f17565e;

    interface b<T, U> {
        U apply(T t10);
    }

    private static class c {

        /* renamed from: a, reason: collision with root package name */
        final String f17566a;

        /* renamed from: b, reason: collision with root package name */
        final String f17567b;

        private c(String str, String str2) {
            this.f17566a = str;
            this.f17567b = str2;
        }
    }

    interface d<T> {
        T a();
    }

    m0(s3.a aVar, s3.a aVar2, e eVar, t0 t0Var, rc.a<String> aVar3) {
        this.f17561a = t0Var;
        this.f17562b = aVar;
        this.f17563c = aVar2;
        this.f17564d = eVar;
        this.f17565e = aVar3;
    }

    private static String A1(Iterable<k> iterable) {
        StringBuilder sb2 = new StringBuilder("(");
        Iterator<k> it = iterable.iterator();
        while (it.hasNext()) {
            sb2.append(it.next().c());
            if (it.hasNext()) {
                sb2.append(',');
            }
        }
        sb2.append(')');
        return sb2.toString();
    }

    static <T> T B1(Cursor cursor, b<Cursor, T> bVar) {
        try {
            return bVar.apply(cursor);
        } finally {
            cursor.close();
        }
    }

    private long C0(SQLiteDatabase sQLiteDatabase, i3.p pVar) {
        Long lQ0 = Q0(sQLiteDatabase, pVar);
        if (lQ0 != null) {
            return lQ0.longValue();
        }
        ContentValues contentValues = new ContentValues();
        contentValues.put("backend_name", pVar.b());
        contentValues.put("priority", Integer.valueOf(t3.a.a(pVar.d())));
        contentValues.put("next_request_ms", (Integer) 0);
        if (pVar.c() != null) {
            contentValues.put("extras", Base64.encodeToString(pVar.c(), 0));
        }
        return sQLiteDatabase.insert("transport_contexts", null, contentValues);
    }

    private l3.b M0() {
        return l3.b.b().b(l3.e.c().b(D0()).c(e.f17544a.f()).a()).a();
    }

    private long N0() {
        return L0().compileStatement("PRAGMA page_count").simpleQueryForLong();
    }

    private long O0() {
        return L0().compileStatement("PRAGMA page_size").simpleQueryForLong();
    }

    private l3.f P0() {
        final long jA = this.f17562b.a();
        return (l3.f) R0(new b() { // from class: q3.c0
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return m0.b1(jA, (SQLiteDatabase) obj);
            }
        });
    }

    private Long Q0(SQLiteDatabase sQLiteDatabase, i3.p pVar) {
        StringBuilder sb2 = new StringBuilder("backend_name = ? and priority = ?");
        ArrayList arrayList = new ArrayList(Arrays.asList(pVar.b(), String.valueOf(t3.a.a(pVar.d()))));
        if (pVar.c() != null) {
            sb2.append(" and extras = ?");
            arrayList.add(Base64.encodeToString(pVar.c(), 0));
        } else {
            sb2.append(" and extras is null");
        }
        return (Long) B1(sQLiteDatabase.query("transport_contexts", new String[]{"_id"}, sb2.toString(), (String[]) arrayList.toArray(new String[0]), null, null, null), new b() { // from class: q3.z
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return m0.c1((Cursor) obj);
            }
        });
    }

    private boolean S0() {
        return N0() * O0() >= this.f17564d.f();
    }

    private List<k> T0(List<k> list, Map<Long, Set<c>> map) {
        ListIterator<k> listIterator = list.listIterator();
        while (listIterator.hasNext()) {
            k next = listIterator.next();
            if (map.containsKey(Long.valueOf(next.c()))) {
                i.a aVarL = next.b().l();
                for (c cVar : map.get(Long.valueOf(next.c()))) {
                    aVarL.c(cVar.f17566a, cVar.f17567b);
                }
                listIterator.set(k.a(next.c(), next.d(), aVarL.d()));
            }
        }
        return list;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ Object U0(Cursor cursor) {
        while (cursor.moveToNext()) {
            b(cursor.getInt(0), c.b.MESSAGE_TOO_OLD, cursor.getString(1));
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ Integer V0(long j10, SQLiteDatabase sQLiteDatabase) {
        String[] strArr = {String.valueOf(j10)};
        B1(sQLiteDatabase.rawQuery("SELECT COUNT(*), transport_name FROM events WHERE timestamp_ms < ? GROUP BY transport_name", strArr), new b() { // from class: q3.r
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return this.f17581a.U0((Cursor) obj);
            }
        });
        return Integer.valueOf(sQLiteDatabase.delete("events", "timestamp_ms < ?", strArr));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ Object W0(SQLiteDatabase sQLiteDatabase) {
        sQLiteDatabase.beginTransaction();
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ Object X0(Throwable th) {
        throw new r3.a("Timed out while trying to acquire the lock.", th);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ SQLiteDatabase Y0(Throwable th) {
        throw new r3.a("Timed out while trying to open db.", th);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ Long Z0(Cursor cursor) {
        return Long.valueOf(cursor.moveToNext() ? cursor.getLong(0) : 0L);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ l3.f a1(long j10, Cursor cursor) {
        cursor.moveToNext();
        return l3.f.c().c(cursor.getLong(0)).b(j10).a();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ l3.f b1(final long j10, SQLiteDatabase sQLiteDatabase) {
        return (l3.f) B1(sQLiteDatabase.rawQuery("SELECT last_metrics_upload_ms FROM global_log_event_state LIMIT 1", new String[0]), new b() { // from class: q3.d0
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return m0.a1(j10, (Cursor) obj);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ Long c1(Cursor cursor) {
        if (cursor.moveToNext()) {
            return Long.valueOf(cursor.getLong(0));
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ Boolean d1(i3.p pVar, SQLiteDatabase sQLiteDatabase) {
        Long lQ0 = Q0(sQLiteDatabase, pVar);
        return lQ0 == null ? Boolean.FALSE : (Boolean) B1(L0().rawQuery("SELECT 1 FROM events WHERE context_id = ? LIMIT 1", new String[]{lQ0.toString()}), new b() { // from class: q3.y
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return Boolean.valueOf(((Cursor) obj).moveToNext());
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ List e1(SQLiteDatabase sQLiteDatabase) {
        return (List) B1(sQLiteDatabase.rawQuery("SELECT distinct t._id, t.backend_name, t.priority, t.extras FROM transport_contexts AS t, events AS e WHERE e.context_id = t._id", new String[0]), new b() { // from class: q3.j0
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return m0.f1((Cursor) obj);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ List f1(Cursor cursor) {
        ArrayList arrayList = new ArrayList();
        while (cursor.moveToNext()) {
            arrayList.add(i3.p.a().b(cursor.getString(1)).d(t3.a.b(cursor.getInt(2))).c(v1(cursor.getString(3))).a());
        }
        return arrayList;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ List g1(i3.p pVar, SQLiteDatabase sQLiteDatabase) {
        List<k> listT1 = t1(sQLiteDatabase, pVar, this.f17564d.d());
        for (g3.d dVar : g3.d.values()) {
            if (dVar != pVar.d()) {
                int iD = this.f17564d.d() - listT1.size();
                if (iD <= 0) {
                    break;
                }
                listT1.addAll(t1(sQLiteDatabase, pVar.f(dVar), iD));
            }
        }
        return T0(listT1, u1(sQLiteDatabase, listT1));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ l3.a h1(Map map, a.C0239a c0239a, Cursor cursor) {
        while (cursor.moveToNext()) {
            String string = cursor.getString(0);
            c.b bVarW0 = w0(cursor.getInt(1));
            long j10 = cursor.getLong(2);
            if (!map.containsKey(string)) {
                map.put(string, new ArrayList());
            }
            ((List) map.get(string)).add(l3.c.c().c(bVarW0).b(j10).a());
        }
        w1(c0239a, map);
        c0239a.e(P0());
        c0239a.d(M0());
        c0239a.c(this.f17565e.get());
        return c0239a.b();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ l3.a i1(String str, final Map map, final a.C0239a c0239a, SQLiteDatabase sQLiteDatabase) {
        return (l3.a) B1(sQLiteDatabase.rawQuery(str, new String[0]), new b() { // from class: q3.b0
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return this.f17539a.h1(map, c0239a, (Cursor) obj);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ Object j1(List list, i3.p pVar, Cursor cursor) {
        while (cursor.moveToNext()) {
            long j10 = cursor.getLong(0);
            boolean z10 = cursor.getInt(7) != 0;
            i.a aVarK = i3.i.a().j(cursor.getString(1)).i(cursor.getLong(2)).k(cursor.getLong(3));
            aVarK.h(z10 ? new i3.h(z1(cursor.getString(4)), cursor.getBlob(5)) : new i3.h(z1(cursor.getString(4)), x1(j10)));
            if (!cursor.isNull(6)) {
                aVarK.g(Integer.valueOf(cursor.getInt(6)));
            }
            list.add(k.a(j10, pVar, aVarK.d()));
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ Object k1(Map map, Cursor cursor) {
        while (true) {
            if (!cursor.moveToNext()) {
                return null;
            }
            long j10 = cursor.getLong(0);
            Set hashSet = (Set) map.get(Long.valueOf(j10));
            if (hashSet == null) {
                hashSet = new HashSet();
                map.put(Long.valueOf(j10), hashSet);
            }
            hashSet.add(new c(cursor.getString(1), cursor.getString(2)));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ Long l1(i3.i iVar, i3.p pVar, SQLiteDatabase sQLiteDatabase) {
        if (S0()) {
            b(1L, c.b.CACHE_FULL, iVar.j());
            return -1L;
        }
        long jC0 = C0(sQLiteDatabase, pVar);
        int iE = this.f17564d.e();
        byte[] bArrA = iVar.e().a();
        boolean z10 = bArrA.length <= iE;
        ContentValues contentValues = new ContentValues();
        contentValues.put("context_id", Long.valueOf(jC0));
        contentValues.put("transport_name", iVar.j());
        contentValues.put("timestamp_ms", Long.valueOf(iVar.f()));
        contentValues.put("uptime_ms", Long.valueOf(iVar.k()));
        contentValues.put("payload_encoding", iVar.e().b().a());
        contentValues.put("code", iVar.d());
        contentValues.put("num_attempts", (Integer) 0);
        contentValues.put("inline", Boolean.valueOf(z10));
        contentValues.put("payload", z10 ? bArrA : new byte[0]);
        long jInsert = sQLiteDatabase.insert("events", null, contentValues);
        if (!z10) {
            int iCeil = (int) Math.ceil(bArrA.length / iE);
            for (int i10 = 1; i10 <= iCeil; i10++) {
                byte[] bArrCopyOfRange = Arrays.copyOfRange(bArrA, (i10 - 1) * iE, Math.min(i10 * iE, bArrA.length));
                ContentValues contentValues2 = new ContentValues();
                contentValues2.put("event_id", Long.valueOf(jInsert));
                contentValues2.put("sequence_num", Integer.valueOf(i10));
                contentValues2.put("bytes", bArrCopyOfRange);
                sQLiteDatabase.insert("event_payloads", null, contentValues2);
            }
        }
        for (Map.Entry<String, String> entry : iVar.i().entrySet()) {
            ContentValues contentValues3 = new ContentValues();
            contentValues3.put("event_id", Long.valueOf(jInsert));
            contentValues3.put("name", entry.getKey());
            contentValues3.put("value", entry.getValue());
            sQLiteDatabase.insert("event_metadata", null, contentValues3);
        }
        return Long.valueOf(jInsert);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ byte[] m1(Cursor cursor) {
        ArrayList arrayList = new ArrayList();
        int length = 0;
        while (cursor.moveToNext()) {
            byte[] blob = cursor.getBlob(0);
            arrayList.add(blob);
            length += blob.length;
        }
        byte[] bArr = new byte[length];
        int length2 = 0;
        for (int i10 = 0; i10 < arrayList.size(); i10++) {
            byte[] bArr2 = (byte[]) arrayList.get(i10);
            System.arraycopy(bArr2, 0, bArr, length2, bArr2.length);
            length2 += bArr2.length;
        }
        return bArr;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ Object n1(Cursor cursor) {
        while (cursor.moveToNext()) {
            b(cursor.getInt(0), c.b.MAX_RETRIES_REACHED, cursor.getString(1));
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ Object o1(String str, String str2, SQLiteDatabase sQLiteDatabase) {
        sQLiteDatabase.compileStatement(str).execute();
        B1(sQLiteDatabase.rawQuery(str2, null), new b() { // from class: q3.v
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return this.f17600a.n1((Cursor) obj);
            }
        });
        sQLiteDatabase.compileStatement("DELETE FROM events WHERE num_attempts >= 16").execute();
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ Boolean p1(Cursor cursor) {
        return Boolean.valueOf(cursor.getCount() > 0);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ Object q1(String str, c.b bVar, long j10, SQLiteDatabase sQLiteDatabase) throws SQLException {
        if (((Boolean) B1(sQLiteDatabase.rawQuery("SELECT 1 FROM log_event_dropped WHERE log_source = ? AND reason = ?", new String[]{str, Integer.toString(bVar.a())}), new b() { // from class: q3.u
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return m0.p1((Cursor) obj);
            }
        })).booleanValue()) {
            sQLiteDatabase.execSQL("UPDATE log_event_dropped SET events_dropped_count = events_dropped_count + " + j10 + " WHERE log_source = ? AND reason = ?", new String[]{str, Integer.toString(bVar.a())});
        } else {
            ContentValues contentValues = new ContentValues();
            contentValues.put("log_source", str);
            contentValues.put("reason", Integer.valueOf(bVar.a()));
            contentValues.put("events_dropped_count", Long.valueOf(j10));
            sQLiteDatabase.insert("log_event_dropped", null, contentValues);
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ Object r1(long j10, i3.p pVar, SQLiteDatabase sQLiteDatabase) {
        ContentValues contentValues = new ContentValues();
        contentValues.put("next_request_ms", Long.valueOf(j10));
        if (sQLiteDatabase.update("transport_contexts", contentValues, "backend_name = ? and priority = ?", new String[]{pVar.b(), String.valueOf(t3.a.a(pVar.d()))}) < 1) {
            contentValues.put("backend_name", pVar.b());
            contentValues.put("priority", Integer.valueOf(t3.a.a(pVar.d())));
            sQLiteDatabase.insert("transport_contexts", null, contentValues);
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ Object s1(SQLiteDatabase sQLiteDatabase) {
        sQLiteDatabase.compileStatement("DELETE FROM log_event_dropped").execute();
        sQLiteDatabase.compileStatement("UPDATE global_log_event_state SET last_metrics_upload_ms=" + this.f17562b.a()).execute();
        return null;
    }

    private List<k> t1(SQLiteDatabase sQLiteDatabase, final i3.p pVar, int i10) {
        final ArrayList arrayList = new ArrayList();
        Long lQ0 = Q0(sQLiteDatabase, pVar);
        if (lQ0 == null) {
            return arrayList;
        }
        B1(sQLiteDatabase.query("events", new String[]{"_id", "transport_name", "timestamp_ms", "uptime_ms", "payload_encoding", "payload", "code", "inline"}, "context_id = ?", new String[]{lQ0.toString()}, null, null, null, String.valueOf(i10)), new b() { // from class: q3.x
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return this.f17602a.j1(arrayList, pVar, (Cursor) obj);
            }
        });
        return arrayList;
    }

    private Map<Long, Set<c>> u1(SQLiteDatabase sQLiteDatabase, List<k> list) {
        final HashMap map = new HashMap();
        StringBuilder sb2 = new StringBuilder("event_id IN (");
        for (int i10 = 0; i10 < list.size(); i10++) {
            sb2.append(list.get(i10).c());
            if (i10 < list.size() - 1) {
                sb2.append(',');
            }
        }
        sb2.append(')');
        B1(sQLiteDatabase.query("event_metadata", new String[]{"event_id", "name", "value"}, sb2.toString(), null, null, null, null), new b() { // from class: q3.s
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return m0.k1(map, (Cursor) obj);
            }
        });
        return map;
    }

    private static byte[] v1(String str) {
        if (str == null) {
            return null;
        }
        return Base64.decode(str, 0);
    }

    private c.b w0(int i10) {
        c.b bVar = c.b.REASON_UNKNOWN;
        if (i10 == bVar.a()) {
            return bVar;
        }
        c.b bVar2 = c.b.MESSAGE_TOO_OLD;
        if (i10 == bVar2.a()) {
            return bVar2;
        }
        c.b bVar3 = c.b.CACHE_FULL;
        if (i10 == bVar3.a()) {
            return bVar3;
        }
        c.b bVar4 = c.b.PAYLOAD_TOO_BIG;
        if (i10 == bVar4.a()) {
            return bVar4;
        }
        c.b bVar5 = c.b.MAX_RETRIES_REACHED;
        if (i10 == bVar5.a()) {
            return bVar5;
        }
        c.b bVar6 = c.b.INVALID_PAYLOD;
        if (i10 == bVar6.a()) {
            return bVar6;
        }
        c.b bVar7 = c.b.SERVER_ERROR;
        if (i10 == bVar7.a()) {
            return bVar7;
        }
        m3.a.b("SQLiteEventStore", "%n is not valid. No matched LogEventDropped-Reason found. Treated it as REASON_UNKNOWN", Integer.valueOf(i10));
        return bVar;
    }

    private void w1(a.C0239a c0239a, Map<String, List<l3.c>> map) {
        for (Map.Entry<String, List<l3.c>> entry : map.entrySet()) {
            c0239a.a(l3.d.c().c(entry.getKey()).b(entry.getValue()).a());
        }
    }

    private byte[] x1(long j10) {
        return (byte[]) B1(L0().query("event_payloads", new String[]{"bytes"}, "event_id = ?", new String[]{String.valueOf(j10)}, null, null, "sequence_num"), new b() { // from class: q3.a0
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return m0.m1((Cursor) obj);
            }
        });
    }

    private void y0(final SQLiteDatabase sQLiteDatabase) {
        y1(new d() { // from class: q3.g0
            @Override // q3.m0.d
            public final Object a() {
                return m0.W0(sQLiteDatabase);
            }
        }, new b() { // from class: q3.h0
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return m0.X0((Throwable) obj);
            }
        });
    }

    private <T> T y1(d<T> dVar, b<Throwable, T> bVar) {
        long jA = this.f17563c.a();
        while (true) {
            try {
                return dVar.a();
            } catch (SQLiteDatabaseLockedException e10) {
                if (this.f17563c.a() >= this.f17564d.b() + jA) {
                    return bVar.apply(e10);
                }
                SystemClock.sleep(50L);
            }
        }
    }

    private static g3.b z1(String str) {
        return str == null ? f17560l : g3.b.b(str);
    }

    long D0() {
        return N0() * O0();
    }

    @Override // q3.d
    public void E0(Iterable<k> iterable) {
        if (iterable.iterator().hasNext()) {
            final String str = "UPDATE events SET num_attempts = num_attempts + 1 WHERE _id in " + A1(iterable);
            final String str2 = "SELECT COUNT(*), transport_name FROM events WHERE num_attempts >= 16 GROUP BY transport_name";
            R0(new b() { // from class: q3.q
                @Override // q3.m0.b
                public final Object apply(Object obj) {
                    return this.f17578a.o1(str, str2, (SQLiteDatabase) obj);
                }
            });
        }
    }

    @Override // q3.d
    public k J(final i3.p pVar, final i3.i iVar) {
        m3.a.c("SQLiteEventStore", "Storing event with priority=%s, name=%s for destination %s", pVar.d(), iVar.j(), pVar.b());
        long jLongValue = ((Long) R0(new b() { // from class: q3.l0
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return this.f17554a.l1(iVar, pVar, (SQLiteDatabase) obj);
            }
        })).longValue();
        if (jLongValue < 1) {
            return null;
        }
        return k.a(jLongValue, pVar, iVar);
    }

    SQLiteDatabase L0() {
        final t0 t0Var = this.f17561a;
        Objects.requireNonNull(t0Var);
        return (SQLiteDatabase) y1(new d() { // from class: q3.w
            @Override // q3.m0.d
            public final Object a() {
                return t0Var.getWritableDatabase();
            }
        }, new b() { // from class: q3.e0
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return m0.Y0((Throwable) obj);
            }
        });
    }

    @Override // q3.d
    public Iterable<i3.p> N() {
        return (Iterable) R0(new b() { // from class: q3.l
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return m0.e1((SQLiteDatabase) obj);
            }
        });
    }

    @Override // q3.d
    public Iterable<k> Q(final i3.p pVar) {
        return (Iterable) R0(new b() { // from class: q3.p
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return this.f17576a.g1(pVar, (SQLiteDatabase) obj);
            }
        });
    }

    <T> T R0(b<SQLiteDatabase, T> bVar) {
        SQLiteDatabase sQLiteDatabaseL0 = L0();
        sQLiteDatabaseL0.beginTransaction();
        try {
            T tApply = bVar.apply(sQLiteDatabaseL0);
            sQLiteDatabaseL0.setTransactionSuccessful();
            return tApply;
        } finally {
            sQLiteDatabaseL0.endTransaction();
        }
    }

    @Override // q3.d
    public boolean T(final i3.p pVar) {
        return ((Boolean) R0(new b() { // from class: q3.k0
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return this.f17552a.d1(pVar, (SQLiteDatabase) obj);
            }
        })).booleanValue();
    }

    @Override // q3.d
    public void Z(final i3.p pVar, final long j10) {
        R0(new b() { // from class: q3.n
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return m0.r1(j10, pVar, (SQLiteDatabase) obj);
            }
        });
    }

    @Override // q3.c
    public void a() {
        R0(new b() { // from class: q3.o
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return this.f17575a.s1((SQLiteDatabase) obj);
            }
        });
    }

    @Override // q3.c
    public void b(final long j10, final c.b bVar, final String str) {
        R0(new b() { // from class: q3.m
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return m0.q1(str, bVar, j10, (SQLiteDatabase) obj);
            }
        });
    }

    @Override // java.io.Closeable, java.lang.AutoCloseable
    public void close() {
        this.f17561a.close();
    }

    @Override // q3.c
    public l3.a e() {
        final a.C0239a c0239aE = l3.a.e();
        final HashMap map = new HashMap();
        final String str = "SELECT log_source, reason, events_dropped_count FROM log_event_dropped";
        return (l3.a) R0(new b() { // from class: q3.t
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return this.f17583a.i1(str, map, c0239aE, (SQLiteDatabase) obj);
            }
        });
    }

    @Override // r3.b
    public <T> T f(b.a<T> aVar) {
        SQLiteDatabase sQLiteDatabaseL0 = L0();
        y0(sQLiteDatabaseL0);
        try {
            T tJ = aVar.j();
            sQLiteDatabaseL0.setTransactionSuccessful();
            return tJ;
        } finally {
            sQLiteDatabaseL0.endTransaction();
        }
    }

    @Override // q3.d
    public int o() {
        final long jA = this.f17562b.a() - this.f17564d.c();
        return ((Integer) R0(new b() { // from class: q3.i0
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return this.f17549a.V0(jA, (SQLiteDatabase) obj);
            }
        })).intValue();
    }

    @Override // q3.d
    public void q(Iterable<k> iterable) {
        if (iterable.iterator().hasNext()) {
            L0().compileStatement("DELETE FROM events WHERE _id in " + A1(iterable)).execute();
        }
    }

    @Override // q3.d
    public long r(i3.p pVar) {
        return ((Long) B1(L0().rawQuery("SELECT next_request_ms FROM transport_contexts WHERE backend_name = ? and priority = ?", new String[]{pVar.b(), String.valueOf(t3.a.a(pVar.d()))}), new b() { // from class: q3.f0
            @Override // q3.m0.b
            public final Object apply(Object obj) {
                return m0.Z0((Cursor) obj);
            }
        })).longValue();
    }
}

# q3.t0
package q3;

import android.content.Context;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import java.util.Arrays;
import java.util.List;

/* loaded from: classes.dex */
final class t0 extends SQLiteOpenHelper {

    /* renamed from: c, reason: collision with root package name */
    private static final String f17587c = "INSERT INTO global_log_event_state VALUES (" + System.currentTimeMillis() + ")";

    /* renamed from: d, reason: collision with root package name */
    static int f17588d = 5;

    /* renamed from: e, reason: collision with root package name */
    private static final a f17589e;

    /* renamed from: l, reason: collision with root package name */
    private static final a f17590l;

    /* renamed from: m, reason: collision with root package name */
    private static final a f17591m;

    /* renamed from: n, reason: collision with root package name */
    private static final a f17592n;

    /* renamed from: o, reason: collision with root package name */
    private static final a f17593o;

    /* renamed from: p, reason: collision with root package name */
    private static final List<a> f17594p;

    /* renamed from: a, reason: collision with root package name */
    private final int f17595a;

    /* renamed from: b, reason: collision with root package name */
    private boolean f17596b;

    public interface a {
        void a(SQLiteDatabase sQLiteDatabase);
    }

    static {
        a aVar = new a() { // from class: q3.o0
            @Override // q3.t0.a
            public final void a(SQLiteDatabase sQLiteDatabase) throws SQLException {
                t0.k(sQLiteDatabase);
            }
        };
        f17589e = aVar;
        a aVar2 = new a() { // from class: q3.p0
            @Override // q3.t0.a
            public final void a(SQLiteDatabase sQLiteDatabase) throws SQLException {
                t0.l(sQLiteDatabase);
            }
        };
        f17590l = aVar2;
        a aVar3 = new a() { // from class: q3.q0
            @Override // q3.t0.a
            public final void a(SQLiteDatabase sQLiteDatabase) throws SQLException {
                sQLiteDatabase.execSQL("ALTER TABLE events ADD COLUMN payload_encoding TEXT");
            }
        };
        f17591m = aVar3;
        a aVar4 = new a() { // from class: q3.r0
            @Override // q3.t0.a
            public final void a(SQLiteDatabase sQLiteDatabase) throws SQLException {
                t0.p(sQLiteDatabase);
            }
        };
        f17592n = aVar4;
        a aVar5 = new a() { // from class: q3.s0
            @Override // q3.t0.a
            public final void a(SQLiteDatabase sQLiteDatabase) throws SQLException {
                t0.w(sQLiteDatabase);
            }
        };
        f17593o = aVar5;
        f17594p = Arrays.asList(aVar, aVar2, aVar3, aVar4, aVar5);
    }

    t0(Context context, String str, int i10) {
        super(context, str, (SQLiteDatabase.CursorFactory) null, i10);
        this.f17596b = false;
        this.f17595a = i10;
    }

    private void j(SQLiteDatabase sQLiteDatabase) {
        if (this.f17596b) {
            return;
        }
        onConfigure(sQLiteDatabase);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void k(SQLiteDatabase sQLiteDatabase) throws SQLException {
        sQLiteDatabase.execSQL("CREATE TABLE events (_id INTEGER PRIMARY KEY, context_id INTEGER NOT NULL, transport_name TEXT NOT NULL, timestamp_ms INTEGER NOT NULL, uptime_ms INTEGER NOT NULL, payload BLOB NOT NULL, code INTEGER, num_attempts INTEGER NOT NULL,FOREIGN KEY (context_id) REFERENCES transport_contexts(_id) ON DELETE CASCADE)");
        sQLiteDatabase.execSQL("CREATE TABLE event_metadata (_id INTEGER PRIMARY KEY, event_id INTEGER NOT NULL, name TEXT NOT NULL, value TEXT NOT NULL,FOREIGN KEY (event_id) REFERENCES events(_id) ON DELETE CASCADE)");
        sQLiteDatabase.execSQL("CREATE TABLE transport_contexts (_id INTEGER PRIMARY KEY, backend_name TEXT NOT NULL, priority INTEGER NOT NULL, next_request_ms INTEGER NOT NULL)");
        sQLiteDatabase.execSQL("CREATE INDEX events_backend_id on events(context_id)");
        sQLiteDatabase.execSQL("CREATE UNIQUE INDEX contexts_backend_priority on transport_contexts(backend_name, priority)");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void l(SQLiteDatabase sQLiteDatabase) throws SQLException {
        sQLiteDatabase.execSQL("ALTER TABLE transport_contexts ADD COLUMN extras BLOB");
        sQLiteDatabase.execSQL("CREATE UNIQUE INDEX contexts_backend_priority_extras on transport_contexts(backend_name, priority, extras)");
        sQLiteDatabase.execSQL("DROP INDEX contexts_backend_priority");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void p(SQLiteDatabase sQLiteDatabase) throws SQLException {
        sQLiteDatabase.execSQL("ALTER TABLE events ADD COLUMN inline BOOLEAN NOT NULL DEFAULT 1");
        sQLiteDatabase.execSQL("DROP TABLE IF EXISTS event_payloads");
        sQLiteDatabase.execSQL("CREATE TABLE event_payloads (sequence_num INTEGER NOT NULL, event_id INTEGER NOT NULL, bytes BLOB NOT NULL,FOREIGN KEY (event_id) REFERENCES events(_id) ON DELETE CASCADE,PRIMARY KEY (sequence_num, event_id))");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void w(SQLiteDatabase sQLiteDatabase) throws SQLException {
        sQLiteDatabase.execSQL("DROP TABLE IF EXISTS log_event_dropped");
        sQLiteDatabase.execSQL("DROP TABLE IF EXISTS global_log_event_state");
        sQLiteDatabase.execSQL("CREATE TABLE log_event_dropped (log_source VARCHAR(45) NOT NULL,reason INTEGER NOT NULL,events_dropped_count BIGINT NOT NULL,PRIMARY KEY(log_source, reason))");
        sQLiteDatabase.execSQL("CREATE TABLE global_log_event_state (last_metrics_upload_ms BIGINT PRIMARY KEY)");
        sQLiteDatabase.execSQL(f17587c);
    }

    private void x(SQLiteDatabase sQLiteDatabase, int i10) {
        j(sQLiteDatabase);
        y(sQLiteDatabase, 0, i10);
    }

    private void y(SQLiteDatabase sQLiteDatabase, int i10, int i11) {
        List<a> list = f17594p;
        if (i11 <= list.size()) {
            while (i10 < i11) {
                f17594p.get(i10).a(sQLiteDatabase);
                i10++;
            }
            return;
        }
        throw new IllegalArgumentException("Migration from " + i10 + " to " + i11 + " was requested, but cannot be performed. Only " + list.size() + " migrations are provided");
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public void onConfigure(SQLiteDatabase sQLiteDatabase) {
        this.f17596b = true;
        sQLiteDatabase.rawQuery("PRAGMA busy_timeout=0;", new String[0]).close();
        sQLiteDatabase.setForeignKeyConstraintsEnabled(true);
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public void onCreate(SQLiteDatabase sQLiteDatabase) {
        x(sQLiteDatabase, this.f17595a);
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public void onDowngrade(SQLiteDatabase sQLiteDatabase, int i10, int i11) throws SQLException {
        sQLiteDatabase.execSQL("DROP TABLE events");
        sQLiteDatabase.execSQL("DROP TABLE event_metadata");
        sQLiteDatabase.execSQL("DROP TABLE transport_contexts");
        sQLiteDatabase.execSQL("DROP TABLE IF EXISTS event_payloads");
        sQLiteDatabase.execSQL("DROP TABLE IF EXISTS log_event_dropped");
        sQLiteDatabase.execSQL("DROP TABLE IF EXISTS global_log_event_state");
        x(sQLiteDatabase, i11);
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public void onOpen(SQLiteDatabase sQLiteDatabase) {
        j(sQLiteDatabase);
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public void onUpgrade(SQLiteDatabase sQLiteDatabase, int i10, int i11) {
        j(sQLiteDatabase);
        y(sQLiteDatabase, i10, i11);
    }
}