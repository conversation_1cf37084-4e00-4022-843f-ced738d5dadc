# i3.d
package i3;

import i3.p;
import java.util.Arrays;

/* loaded from: classes.dex */
final class d extends p {

    /* renamed from: a, reason: collision with root package name */
    private final String f11914a;

    /* renamed from: b, reason: collision with root package name */
    private final byte[] f11915b;

    /* renamed from: c, reason: collision with root package name */
    private final g3.d f11916c;

    static final class b extends p.a {

        /* renamed from: a, reason: collision with root package name */
        private String f11917a;

        /* renamed from: b, reason: collision with root package name */
        private byte[] f11918b;

        /* renamed from: c, reason: collision with root package name */
        private g3.d f11919c;

        b() {
        }

        @Override // i3.p.a
        public p a() {
            String str = "";
            if (this.f11917a == null) {
                str = " backendName";
            }
            if (this.f11919c == null) {
                str = str + " priority";
            }
            if (str.isEmpty()) {
                return new d(this.f11917a, this.f11918b, this.f11919c);
            }
            throw new IllegalStateException("Missing required properties:" + str);
        }

        @Override // i3.p.a
        public p.a b(String str) {
            if (str == null) {
                throw new NullPointerException("Null backendName");
            }
            this.f11917a = str;
            return this;
        }

        @Override // i3.p.a
        public p.a c(byte[] bArr) {
            this.f11918b = bArr;
            return this;
        }

        @Override // i3.p.a
        public p.a d(g3.d dVar) {
            if (dVar == null) {
                throw new NullPointerException("Null priority");
            }
            this.f11919c = dVar;
            return this;
        }
    }

    private d(String str, byte[] bArr, g3.d dVar) {
        this.f11914a = str;
        this.f11915b = bArr;
        this.f11916c = dVar;
    }

    @Override // i3.p
    public String b() {
        return this.f11914a;
    }

    @Override // i3.p
    public byte[] c() {
        return this.f11915b;
    }

    @Override // i3.p
    public g3.d d() {
        return this.f11916c;
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (!(obj instanceof p)) {
            return false;
        }
        p pVar = (p) obj;
        if (this.f11914a.equals(pVar.b())) {
            if (Arrays.equals(this.f11915b, pVar instanceof d ? ((d) pVar).f11915b : pVar.c()) && this.f11916c.equals(pVar.d())) {
                return true;
            }
        }
        return false;
    }

    public int hashCode() {
        return ((((this.f11914a.hashCode() ^ 1000003) * 1000003) ^ Arrays.hashCode(this.f11915b)) * 1000003) ^ this.f11916c.hashCode();
    }
}