# Advanced .so Analysis with IDA Pro

## Phase 1: Setup and Loading

### 1. Load .so Files
```
File -> Open
Select: libapp.so (x86_64 version)
Processor: x86-64
Kernel: ELF for x86-64
```

### 2. Initial Analysis
```
Options -> General -> Analysis
Enable:
- Make function calls
- Make function tails
- Analyze and create all xrefs
- Create stack variables
```

## Phase 2: Advanced IDA Techniques

### 1. Hex-Rays Decompiler
```
F5 on any function for C-like pseudocode
Focus on:
- String manipulation functions
- Network-related functions
- JNI bridge functions
```

### 2. Cross-References
```
Ctrl+X on any function/string
Shows all references and usage patterns
```

### 3. String Analysis
```
View -> Open subviews -> Strings
Filter for phone/API related strings
Double-click to see usage context
```

## Phase 3: Specific IDA Features

### 1. Function Signatures
```
Right-click function -> Set function type
Define proper prototypes for better analysis
```

### 2. Structures and Types
```
View -> Open subviews -> Structures
Define custom structures for API objects
```

### 3. Scripting with IDAPython
```python
# Find all string references containing "api"
import idautils
import idc

for string_addr in idautils.Strings():
    string_val = idc.get_strlit_contents(string_addr.ea)
    if b"api" in string_val.lower():
        print(f"API string at {hex(string_addr.ea)}: {string_val}")
```
