# strings libapp.so | grep -i -E "(phone|api|http|contact|lookup)" > api_search.txt
ApiError
_lookupOpenPorts
_lookupHandler
IAPItem.fromJSON
dev.flutter.pigeon.firebase_auth_platform_interface.FirebaseAuthUserHostApi.getIdToken
_HttpClient@13463476.
dev.flutter.pigeon.SharedPreferencesApi.setString
WebViewFlutterApi
dev.flutter.pigeon.webview_flutter_android.InstanceManagerHostApi.clear^
findUserContactNoneSent
_HttpInboundMessageListInt@13463476
https://zoneb.zain-service.com/api/dropper/prop
PlatformConfigurationNativeApi::SetNeedsReportTimings
androidShareContactStatus
init:api
https_proxy
PlatformConfigurationNativeApi::ScheduleFrame
https:N
<EMAIL>
_FirebaseAuthUserHostApiCodec@656223271
WebViewHostApiImpl
Failed to parse HTTP, 
PermissionRequestFlutterApiImpl
getRegionInfoFromPhoneNumber
dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setJavaScriptCanOpenWindowsAutomatically
PhoneResponse
package:intl_phone_number_input/src/utils/phone_number/phone_number_util.dart
JavaObjectFlutterApiImpl
package:libphonenumber_plugin/src/phone_number_util.dart
PhoneNumberFormat.
PhoneNumber
WebStorageHostApiImpl
fileChooserParamsFlutterApi
DownloadListenerFlutterApi
_HttpOutgoing@13463476.
dart:_http
http:
https://zoneb.zain-service.com/api/v2/search/dodome
package:intl_phone_number_input/src/widgets/input_widget.dart
You must call `databaseFactory = databaseFactoryFfi;` before using global openDatabase API
LaunchContacts
isValidPhoneNumber
WebViewFlutterApiImpl
_httpConnectionHook@13463476
_ContactPermissionScreenState@531203053.
package:http_parser/src/utils.dart
dev.flutter.pigeon.webview_flutter_android.PermissionRequestHostApi.deny
dev.flutter.pigeon.webview_flutter_android.WebViewFlutterApi.create
FirebaseCoreHostApi
viewFlutterApi
setAndroidAllowContact
Trying to set 'Transfer-Encoding: Chunked' on HTTP 1.0 headers
HttpProfile
set:downloadListenerFlutterApi
dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setJavaScriptEnabled
HttpTimelineLoggingState
package:http/src/byte_stream.dart
_getHttpVersion@13463476
LookupBoundary
loadAllContactToDb
_$UserContactDao@407061390.
dev.flutter.pigeon.url_launcher_android.UrlLauncherApi.openUrlInApp
dev.flutter.pigeon.firebase_auth_platform_interface.FirebaseAuthHostApi.verifyPhoneNumber
_HttpOutboundMessage@13463476.
set:fileChooserParamsFlutterApi
https://twitter.com/
PhoneNumber(phoneNumber: 
InternationalPhoneNumberInput
UserContactDao
_HttpConnectionInfo@13463476
dev.flutter.pigeon.webview_flutter_android.JavaObjectFlutterApi.dispose
FileChooserParamsFlutterApiImpl
set:webChromeClientFlutterApi
HTTP connection timed out after 
http://ip-api.com/json?fields=status,country,countryCode,lat,lon,isp,mobile,proxy
Developer_lookupExtension
set:geolocationPermissionsCallbackFlutterApi
plugin.libphonenumber
package:apps/models/service/phone_names.g.dart
get:_isHttps@0150898
HttpDate
package:http/src/base_client.dart
_HttpProfileData@13463476
HTTPS_PROXY
_httpOverridesToken@13463476
dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.requestLoading
ext.dart.io.getHttpProfile
GeolocationPermissionsCallbackHostApiImpl
https://wa.me/
assets/lottie/contacts-permission.json
package:http/src/response.dart
get:_isHttp@0150898
HttpProfileRequest
HttpClientRequest
TextCapitalization
init:_httpConnectionHook@13463476
https://zoneb.zain-service.com/api/users/delete-request
_setHttpEnableTimelineLogging@14069316
package:apps/db/dao/user_contact_dao.dart
HttpClient
PermissionRequestFlutterApi
webViewClientFlutterApi
MicrophoneVolumeMute
SharedPreferencesApi
Invalid HTTP header field name: 
_ContactPermissionScreenState@531203053
ext.dart.io.setHttpEnableTimelineLogging
dev.flutter.pigeon.webview_flutter_android.CustomViewCallbackHostApi.onCustomViewHidden
MicrophoneVolumeDown
dev.flutter.pigeon.webview_flutter_android.GeolocationPermissionsCallbackFlutterApi.create
JavaObjectHostApiImpl.
set:customViewCallbackFlutterApi
LibPhoneNumberPlatform.
flutter.io.allow_http
_WebChromeClientFlutterApiCodec@**********
dart._http
_reportHttpError@13463476
dev.flutter.pigeon.webview_flutter_android.WebChromeClientHostApi.create
contacts
package:http/src/io_client.dart
_$PhoneResponseToJson@425491576
dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.onPageFinished
FirebaseAuthUserHostApi
package:http/src/io_streamed_response.dart
_$UserContactToJson@406158212
PhoneNames
LaunchPhone
invalid-phone-number
_lookupOpenPorts@1026248
HttpClient.
dev.flutter.pigeon.webview_flutter_android.JavaScriptChannelFlutterApi.postMessage
staggeredLookup
normalizePhoneNumber
This happens when a plugin sends messages to the framework side before the framework has had an opportunity to register a listener. See the ChannelBuffers API documentation for details on how to configure the channel to expect more messages, or to expect messages to get discarded:
  https://api.flutter.dev/flutter/dart-ui/ChannelBuffers-class.html
_HttpOutboundMessage@13463476
dev.flutter.pigeon.firebase_auth_platform_interface.FirebaseAuthHostApi.registerAuthStateListener
package:intl_phone_number_input/src/utils/util.dart
PigeonPhoneMultiFactorAssertion
LocalPhoneName
dev.flutter.pigeon.firebase_auth_platform_interface.FirebaseAuthHostApi.registerIdTokenListener
PlatformConfigurationNativeApi::SendPlatformMessage
_HashMapIterator@3220832
_HttpProfileData@13463476.
textCapitalization
formattedPhoneNumber
dev.flutter.pigeon.firebase_auth_platform_interface.FirebaseAuthUserHostApi.reload
webChromeClientFlutterApi
customViewCallbackFlutterApi
phoneNames
PhoneNames.fromJson
JavaObjectHostApiImpl
package:http_parser/src/scan.dart
HttpProfiler
Auth#phoneVerificationFailed
android_allow_contact
apiKey
_HttpClientConnection@13463476
WebSettingsHostApi
init:_httpOverridesToken@13463476
dart:_http/http_impl.dart
dev.flutter.pigeon.webview_flutter_android.DownloadListenerFlutterApi.onDownloadStart
dev.flutter.pigeon.SharedPreferencesApi.remove
dev.flutter.pigeon.webview_flutter_android.FileChooserParamsFlutterApi.create
https
PhoneNumbersParsing|formatPhone
set:webViewFlutterApi
downloadListenerFlutterApi
TextCapitalization.none
package:apps/models/db/user_contact.dart
IAPItem
getPhoneImgAsset
parsePhoneNumber
_getHttpEnableTimelineLogging@14069316
get:userContactDao
package:firebase_auth_platform_interface/src/providers/phone_auth.dart
https://zoneb.zain-service.com/api/%s/save
WebChromeClientHostApi
_HttpProfileEvent@13463476.
https://zoneb.zain-service.com/api/%s/all-by-code?code=%s
UserContactService
dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.onPageStarted
PigeonVerifyPhoneNumberRequest
JavaScriptChannelHostApi
_HttpOutgoing@13463476
package:flutter/src/widgets/lookup_boundary.dart
_$PhoneNamesToJson@425491576
Auth#phoneCodeSent
package:apps/models/local_phone_name.dart
PhoneResponse.fromJson
MicrophoneVolumeUp
package:webview_flutter_android/src/android_webview_api_impls.dart
_HttpHeaders@13463476
package:sqflite_common/sqlite_api.dart
HTTP CLIENT response of 
dev.flutter.pigeon.webview_flutter_android.JavaScriptChannelHostApi.create
dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setDomStorageEnabled
dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.onReceivedRequestError
addPhoneLoginFirebaseUser
dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setSupportMultipleWindows
_HttpClient@13463476
PhoneMultiFactorInfo
WebStorageHostApi
ext.dart.io.getHttpEnableTimelineLogging
https://callersapps.firebaseio.com
MethodChannelLibPhoneNumber.
_HttpHeaders@13463476.
PermissionRequestHostApiImpl
dev.flutter.pigeon.webview_flutter_android.CustomViewCallbackFlutterApi.create
package:apps/models/service/phone_names.dart
dev.flutter.pigeon.webview_flutter_android.JavaObjectHostApi.dispose
package:libphonenumber_platform_interface/src/utils/enums/phone_number_format.dart
AndroidWebViewFlutterApis
package:intl_phone_number_input/src/utils/phone_number.dart
dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.doUpdateVisitedHistory
lookupAddresses
_HttpClientResponse@13463476.
PhoneNumberUtil
package:http_parser/src/case_insensitive_map.dart
Invalid response line, failed to parse HTTP version
_HttpClientRequest@13463476.
dev.flutter.pigeon.url_launcher_android.UrlLauncherApi.launchUrl
_HttpIncoming@13463476.
set:javaScriptChannelFlutterApi
dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.loadDataWithBaseUrl
InstanceManagerHostApi
lookUpLayout
dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.onReceivedError
dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.postUrl
http_proxy
_FirebaseCoreHostApiCodec@671305088
dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onPermissionRequest
package:intl_phone_number_input/src/utils/formatter/as_you_type_formatter.dart
PermissionRequestHostApi
package:intl_phone_number_input/src/models/country_model.dart
WebChromeClientHostApiImpl
HTTP_PROXY
phone
_setPhone@597179013
PhoneNumber.
WebViewClientFlutterApiImpl
PhoneAuthCredential
MethodChannelLibPhoneNumber
Non-https connection "
HttpException
WebSettingsHostApiImpl
PhoneNumbersParsing|parseArabicNumbers
package:http/src/base_response.dart6
phoneNumberControllerListener
dart:_http/http_parser.dart
package:http/src/utils.dart
_HttpClientConnection@13463476.
_HttpClientRequest@13463476
set:permissionRequestFlutterApi
JavaScriptChannelFlutterApiImpl
intl_phone_number_input
_HttpParser@13463476
Auth#phoneCodeAutoRetrievalTimeout
Trying to clear ContentLength on HTTP 1.0 headers with 'Connection: Keep-Alive' set
WebViewHostApi
WebChromeClientFlutterApi
dev.flutter.pigeon.webview_flutter_android.PermissionRequestFlutterApi.create
PhoneNumberFormat
dev.flutter.pigeon.webview_flutter_android.WebViewClientHostApi.create
ext.dart.io.httpEnableTimelineLogging
_UrlLauncherApiCodec@1119207135
dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.setBackgroundColor
dart:_http/http_headers.dart
https://zoneb.zain-service.com/api/notify-me/unregister/%s
getHttpProfileRequest
HttpClientResponse
FileChooserParamsFlutterApi
DownloadListenerFlutterApiImpl
CustomViewCallbackFlutterApi
dart:_http/overrides.dart
get:_httpConnectionHook@13463476
package:libphonenumber_platform_interface/src/utils/classes/region_info.dart
Phone number
package:intl_phone_number_input/src/widgets/item.dart
HttpHeaders
package:http/src/base_request.dart
_HashMapIterable@3220832
DownloadListenerHostApi
ContactPermissionScreen
ext.dart.io.getHttpProfileRequest
dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setUseWideViewPort
CustomViewCallbackHostApi
dev.flutter.pigeon.webview_flutter_android.GeolocationPermissionsCallbackHostApi.invoke
DownloadListenerHostApiImpl
_HttpProfileEvent@13463476
dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setBuiltInZoomControls
package:intl_phone_number_input/src/models/country_list.dart
, phoneNumber: 
PlatformConfigurationNativeApi::RequestDartPerformanceMode
_phoneDecoration@460012031
_$UserContactDao@407061390
https://instagram.com/
SELECT * FROM UserContact where sent = 0 limit 100
PhoneInputSelectorType.
https://zoneb.zain-service.com/api/v2/search-biz/work-biz/
dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setLoadWithOverviewMode
dart:_http/http_date.dart
syncContacts
get:phoneNumber
dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onShowFileChooser
permissionRequestFlutterApi
dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onGeolocationPermissionsHidePrompt
dev.flutter.pigeon.firebase_auth_platform_interface.FirebaseAuthHostApi.signOut
_HttpParser@13463476._@13463476
HttpConnectionInfo
_$PhoneNamesFromJson@425491576
package:http/src/exception.dart
HTTP/client
ViewFlutterApi
PlatformConfigurationNativeApi::RespondToPlatformMessage
PlatformConfigurationNativeApi::UpdateSemantics
_getHttpConnectionHookClosure@15065589
https://zoneb.zain-service.com/api/notify-me/is-register/%s
dev.flutter.pigeon.webview_flutter_android.WebStorageHostApi.create
buildPhone
geolocationPermissionsCallbackFlutterApi
package:intl_phone_number_input/src/utils/selector_config.dart
dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.setWebChromeClient
dev.flutter.pigeon.SharedPreferencesApi.setBool
dev.flutter.pigeon.webview_flutter_android.ViewFlutterApi.create
https://zoneb.zain-service.com/api/v2/search-biz/work-action?phone=%s&action=%s
  https://api.flutter.dev/flutter/material/Scaffold/of.html
package:http/src/streamed_response.dart
PlatformConfigurationNativeApi::DefaultRouteName
_HttpClientResponse@13463476
MicrophoneToggle
dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.loadUrl
https://zoneb.zain-service.com/api/users/searchable-update/%s
Trying to set 'Connection: Keep-Alive' on HTTP 1.0 headers with no ContentLength
WebChromeClientFlutterApiImpl
package:http_parser/src/media_type.dart
_checkContactPermission@531203053
UserContact
MicrophoneMuteToggle
set:viewFlutterApi
https://zoneb.zain-service.com/api/notify-me/register/%s
Failed host lookup: '
JavaObjectFlutterApi
verifyPhoneNumber
dev.flutter.pigeon.webview_flutter_android.DownloadListenerHostApi.create
_WebViewHostApiCodec@**********
HttpClientResponseCompressionState
; HttpOnly
package:libphonenumber_platform_interface/src/libphonenumber_platform.dart
ext.dart.io.clearHttpProfile
AndroidWebViewFlutterApis.
PlatformConfigurationNativeApi::ImplicitViewEnabled
http
PlatformConfigurationNativeApi::GetRootIsolateToken
dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onGeolocationPermissionsShowPrompt
MessageLookup
WebViewClientHostApi
getParsedPhoneNumber
_$PhoneResponseFromJson@425491576
View the documentation for more information: https://firebase.flutter.dev/docs/overview#initialization
dev.flutter.pigeon.firebase_auth_platform_interface.FirebaseAuthUserHostApi.updateProfile
package:http/src/request.dart
PhoneAuthProvider
httponly
iphone
set:javaObjectFlutterApi
package:http/src/client.dart 
dev.flutter.pigeon.SharedPreferencesApi.getAll
dev.flutter.pigeon.FirebaseCoreHostApi.initializeCore
package:http/http.dart
microphone
_lookupExtension@5383715
JavaObjectHostApi
dev.flutter.pigeon.firebase_auth_platform_interface.FirebaseAuthHostApi.signInAnonymously
HTTP CLIENT 
dev.flutter.pigeon.firebase_auth_platform_interface.FirebaseAuthHostApi.signInWithCredential
set:webViewClientFlutterApi
LibPhoneNumberPlatform
_FirebaseAuthHostApiCodec@656223271
package:apps/services/user_contact_service.dart
phoneName
dev.flutter.pigeon.FirebaseCoreHostApi.initializeApp
package:intl_phone_number_input/src/widgets/selector_button.dart
InternationalPhoneNumberInput.
https://zoneb.zain-service.com/api/users/getmy-names/%s
package:intl_phone_number_input/src/utils/widget_view.dart
andi_allow_contact
https://zoneb.zain-service.com/api/users/searchable-getval/%s/%s
GeolocationPermissionsCallbackFlutterApi
dart:_http/embedder_config.dart
updateUserContact
phoneNumber
package:apps/models/db/user_contact.g.dart
HTTP headers are not mutable
package:intl_phone_number_input/src/utils/test/test_helper.dart
https://wa.me/965
FirebaseAuthHostApi
GeolocationPermissionsCallbackHostApi
Invalid response, invalid HTTP version
ViewFlutterApiImpl
_getHttpProfileRequest@14069316
dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onShowCustomView
package:libphonenumber_platform_interface/src/method_channel/libhonenumber_method_channel.dart
PhoneAuthCredential._credentialFromToken@661224542
HttpException: 
WebViewClientHostApiImpl
PhoneInputSelectorType
webViewFlutterApi
dev.flutter.pigeon.webview_flutter_android.WebViewHostApi.create
dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.setDisplayZoomControls
HTTP request failed. Client is already closed.
TextCapitalization.
GeolocationPermissionsCallbackFlutterApiImpl
package:url_launcher/src/legacy_api.dart
Auth#phoneVerificationCompleted
PlatformConfigurationNativeApi::Render
androidGetContactStatus
WebViewClientFlutterApi
" is not supported by the platform. Refer to https://flutter.dev/docs/release/breaking-changes/network-policy-ios-android.
Invalid HTTP header field value: 
_lookupHandler@1026248
HttpOverrides
lookup
dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onHideCustomView
dev.flutter.pigeon.webview_flutter_android.WebSettingsHostApi.create
CustomViewCallbackHostApiImpl
HttpClientResponseCompressionState.
package:intl_phone_number_input/src/providers/country_provider.dart
javaScriptChannelFlutterApi
CustomViewCallbackFlutterApiImpl
_WebViewClientFlutterApiCodec@**********
dev.flutter.pigeon.SharedPreferencesApi.setInt
   Please write phone details below the line to review  -------- 
_HttpIncoming@13463476
UrlLauncherApi
Unknown ColorFilter type. This is an error. If you're seeing this, please file an issue at https://github.com/flutter/flutter/issues/new.
https://user-data.meno-dag.com/api/users/download-request/%s/%s?tok=%s
CREATE TABLE IF NOT EXISTS `UserContact` (`phoneName` TEXT, `name` TEXT, `phone` TEXT, `code` TEXT, `sent` INTEGER NOT NULL, PRIMARY KEY (`phoneName`))
JavaScriptChannelFlutterApi
JavaScriptChannelHostApiImpl
dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onProgressChanged
javaObjectFlutterApi
PhoneAuthCredential._credential@661224542
dev.flutter.pigeon.webview_flutter_android.WebViewClientFlutterApi.urlLoading
dev.flutter.pigeon.webview_flutter_android.WebChromeClientFlutterApi.onConsoleMessage
PlaceholderSpan
Sunday
incremental
package:lottie/src/parser/gradient_fill_parser.dart
package:flutter_inapp_purchase/flutter_inapp_purchase.dart
done() must not be called more than once on the same
https://zoneb.zain-service.com/api/v2/search/find/KW
updateMaterialState
paste
Convert your Illustrator layers to shape layers.
obfuscatedProfileId
get:wantKeepAlive
_RandomAccessFileOpsImpl@14069316
endTransaction
before
get:selectionHeightStyle
, transactionReceipt:
RewardedAdLoadCallback
_updateCustomAction@15065589
_dayFromYear@0150898
IntrinsicWidth
sprintf
keyboard
MRWS
b+YqQ
first
widthPx
get:toolbarTextStyle
RepaintBoundary
__Int32ArrayView&_TypedListView&_IntListMixin&_TypedIntListMixin@7027147
QAPI
QAPI
QAPI
QAPI
QAPI
QAPI
QAPI
QAPI
QAPI
QAPI
