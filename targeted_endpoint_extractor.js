// Targeted Endpoint Extractor - Specifically targets Google CCT and Firebase endpoints
// Based on static analysis of data_transport.md and request_get.md

Java.perform(function() {
    console.log("[+] Starting targeted endpoint extraction...");
    
    var discoveredEndpoints = [];
    var apiKeys = [];
    var transportConfigs = [];
    
    // ============= TARGET GOOGLE CCT TRANSPORT =============
    try {
        // Hook the CCT transport backend URL construction
        var URL = Java.use("java.net.URL");
        
        // Monitor URL creation for CCT endpoints
        URL.$init.overload('java.lang.String').implementation = function(spec) {
            if (spec && spec.includes("http")) {
                // Check for Google/Firebase endpoints
                if (spec.includes("google") || spec.includes("firebase") || 
                    spec.includes("googleapis") || spec.includes("gstatic")) {
                    
                    console.log("[CCT_ENDPOINT] " + spec);
                    discoveredEndpoints.push({
                        url: spec,
                        type: "CCT_TRANSPORT",
                        timestamp: new Date().toISOString()
                    });
                }
                
                // Check for any other external endpoints
                if (!spec.includes("127.0.0.1") && !spec.includes("localhost") &&
                    !spec.includes("google") && !spec.includes("firebase")) {
                    
                    console.log("[EXTERNAL_ENDPOINT] " + spec);
                    discoveredEndpoints.push({
                        url: spec,
                        type: "EXTERNAL",
                        timestamp: new Date().toISOString()
                    });
                }
            }
            
            return this.$init(spec);
        };
        
        console.log("[+] CCT transport URL monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook URL: " + e.message);
    }
    
    // ============= TARGET FIREBASE PERFORMANCE =============
    try {
        // Hook Firebase Performance HTTP client
        var FirebasePerfHttpClient = Java.use("com.google.firebase.perf.network.FirebasePerfHttpClient");
        
        // This might not work if the class is obfuscated, but worth trying
        console.log("[+] Found FirebasePerfHttpClient class");
        
        // Try to hook any execute methods
        var methods = FirebasePerfHttpClient.class.getDeclaredMethods();
        for (var i = 0; i < methods.length; i++) {
            console.log("[FIREBASE_METHOD] " + methods[i].getName());
        }
        
    } catch (e) {
        console.log("[-] FirebasePerfHttpClient not accessible: " + e.message);
    }
    
    // ============= TARGET HTTP CLIENT EXECUTE METHODS =============
    try {
        // Hook Apache HttpClient if present
        var HttpClient = Java.use("org.apache.http.client.HttpClient");
        
        // This is an interface, so we need to hook implementations
        console.log("[+] Found HttpClient interface");
        
    } catch (e) {
        console.log("[-] Apache HttpClient not found: " + e.message);
    }
    
    // ============= TARGET OKHTTP (if present) =============
    try {
        var OkHttpClient = Java.use("okhttp3.OkHttpClient");
        var Call = Java.use("okhttp3.Call");
        var Request = Java.use("okhttp3.Request");
        
        // Hook OkHttp newCall
        OkHttpClient.newCall.implementation = function(request) {
            var call = this.newCall(request);
            var url = request.url().toString();
            
            console.log("[OKHTTP_REQUEST] " + url);
            discoveredEndpoints.push({
                url: url,
                type: "OKHTTP",
                method: request.method(),
                timestamp: new Date().toISOString()
            });
            
            return call;
        };
        
        console.log("[+] OkHttp monitoring active");
    } catch (e) {
        console.log("[-] OkHttp not found: " + e.message);
    }
    
    // ============= TARGET SPECIFIC GOOGLE SERVICES =============
    try {
        // Hook HttpURLConnection for Google services
        var HttpURLConnection = Java.use("java.net.HttpURLConnection");
        
        // Hook connect method
        HttpURLConnection.connect.implementation = function() {
            var url = this.getURL();
            if (url) {
                var urlStr = url.toString();
                
                // Log all Google/Firebase connections
                if (urlStr.includes("google") || urlStr.includes("firebase") ||
                    urlStr.includes("googleapis") || urlStr.includes("gstatic")) {
                    
                    console.log("[GOOGLE_CONNECTION] " + urlStr);
                    console.log("  Method: " + this.getRequestMethod());
                    console.log("  User-Agent: " + this.getRequestProperty("User-Agent"));
                    console.log("  Content-Type: " + this.getRequestProperty("Content-Type"));
                    
                    discoveredEndpoints.push({
                        url: urlStr,
                        type: "GOOGLE_SERVICE",
                        method: this.getRequestMethod(),
                        userAgent: this.getRequestProperty("User-Agent"),
                        contentType: this.getRequestProperty("Content-Type"),
                        timestamp: new Date().toISOString()
                    });
                }
            }
            
            return this.connect();
        };
        
        // Hook setRequestProperty for API keys
        HttpURLConnection.setRequestProperty.implementation = function(key, value) {
            if (key && value) {
                // Capture Google API keys
                if (key.includes("X-Goog-Api-Key") || key.includes("Authorization")) {
                    console.log("[API_KEY] " + key + ": " + value);
                    apiKeys.push({
                        header: key,
                        value: value,
                        url: this.getURL() ? this.getURL().toString() : "unknown",
                        timestamp: new Date().toISOString()
                    });
                }
            }
            
            return this.setRequestProperty(key, value);
        };
        
        console.log("[+] Google services monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook Google services: " + e.message);
    }
    
    // ============= TARGET TRANSPORT CONFIGURATION =============
    try {
        // Hook ContentValues for transport config (from data_transport.md analysis)
        var ContentValues = Java.use("android.content.ContentValues");
        
        ContentValues.put.overload('java.lang.String', 'java.lang.String').implementation = function(key, value) {
            if (key && value) {
                // Monitor transport configuration
                if (key === "backend_name" || key === "transport_name" || key === "extras") {
                    console.log("[TRANSPORT_CONFIG] " + key + ": " + value);
                    transportConfigs.push({
                        key: key,
                        value: value,
                        timestamp: new Date().toISOString()
                    });
                }
            }
            
            return this.put(key, value);
        };
        
        console.log("[+] Transport configuration monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook ContentValues: " + e.message);
    }
    
    // ============= PERIODIC REPORTING =============
    var reportCount = 0;
    var reportInterval = setInterval(function() {
        reportCount++;
        
        console.log("\n" + "=".repeat(80));
        console.log("TARGETED ENDPOINT EXTRACTION REPORT #" + reportCount);
        console.log("=".repeat(80));
        
        if (discoveredEndpoints.length > 0) {
            console.log("\n[DISCOVERED ENDPOINTS] (" + discoveredEndpoints.length + " total):");
            discoveredEndpoints.forEach(function(endpoint, index) {
                console.log("  " + (index + 1) + ". [" + endpoint.type + "] " + endpoint.url);
                if (endpoint.method) console.log("     Method: " + endpoint.method);
                if (endpoint.userAgent) console.log("     User-Agent: " + endpoint.userAgent);
                console.log("     Time: " + endpoint.timestamp);
                console.log("");
            });
        } else {
            console.log("\n[NO ENDPOINTS DISCOVERED YET]");
            console.log("Try interacting with the app to trigger network requests");
        }
        
        if (apiKeys.length > 0) {
            console.log("\n[API KEYS CAPTURED] (" + apiKeys.length + " total):");
            apiKeys.forEach(function(key, index) {
                console.log("  " + (index + 1) + ". " + key.header + ": " + key.value.substring(0, 30) + "...");
                console.log("     URL: " + key.url);
                console.log("     Time: " + key.timestamp);
                console.log("");
            });
        }
        
        if (transportConfigs.length > 0) {
            console.log("\n[TRANSPORT CONFIGURATIONS] (" + transportConfigs.length + " total):");
            transportConfigs.forEach(function(config, index) {
                console.log("  " + (index + 1) + ". " + config.key + ": " + config.value);
                console.log("     Time: " + config.timestamp);
                console.log("");
            });
        }
        
        console.log("=".repeat(80));
        
        // Stop after 8 reports (2 minutes)
        if (reportCount >= 8) {
            clearInterval(reportInterval);
            console.log("[+] Targeted endpoint extraction completed");
            
            // Final summary
            console.log("\n[FINAL SUMMARY]");
            console.log("Total endpoints discovered: " + discoveredEndpoints.length);
            console.log("Total API keys captured: " + apiKeys.length);
            console.log("Total transport configs: " + transportConfigs.length);
        }
        
    }, 15000); // Report every 15 seconds
    
    console.log("[+] Targeted endpoint extractor running...");
    console.log("[+] Will report every 15 seconds for 2 minutes");
    console.log("[+] Interact with the app to trigger network requests");
});
