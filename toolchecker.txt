# strings libtoolChecker.so

Android
r21b
6352462
__cxa_atexit
LIBC
libc.so
libtoolChecker.so
__cxa_finalize
Java_com_scottyab_rootbeer_RootBeerNative_checkForRoot
Java_com_scottyab_rootbeer_RootBeerNative_setLogDebugMessages
_Z6existsPKc
__android_log_print
__stack_chk_fail
fclose
fopen
_edata
__bss_start
_end
libandroid.so
liblog.so
libm.so
libdl.so
lCZBE
AVSPI
ffffff.
UAWAVAUATSH
D$ H
D9l$
H;L$ u
([A\A]A^A_]
RootBeer
LOOKING FOR BINARY: %s PRESENT!!!
LOOKING FOR BINARY: %s Absent :(
;*3$"
Android (6317467 based on r365631c1) clang version 9.0.8 (https://android.googlesource.com/toolchain/llvm-project e0caee08e5f09b374a27a676d04978c81fcb1928) (based on LLVM 9.0.8svn)
gold 1.12
.fini_array
.text
.got
.comment
.note.android.ident
.got.plt
.rela.plt
.bss
.dynstr
.eh_frame_hdr
.gnu.version_r
.data.rel.ro
.rela.dyn
.gnu.version
.note.gnu.gold-version
.dynsym
.gnu.hash
.eh_frame
.note.gnu.build-id
.gnu.version_d
.dynamic
.shstrtab
.rodata