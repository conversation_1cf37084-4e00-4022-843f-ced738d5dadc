// Deep HTTP Monitor - Captures HTTP requests through any available method
// Targets all possible HTTP libraries and methods used by Flutter/hybrid apps

Java.perform(function() {
    console.log("[+] Deep HTTP Monitor starting...");
    
    var httpRequests = [];
    var socketConnections = [];
    var dnsLookups = [];
    
    function isPhoneRelated(str) {
        if (!str) return false;
        var lowerStr = str.toLowerCase();
        return lowerStr.includes("phone") || lowerStr.includes("caller") ||
               lowerStr.includes("lookup") || lowerStr.includes("contact") ||
               lowerStr.includes("number") || lowerStr.includes("search") ||
               lowerStr.includes("api") || lowerStr.includes("spam") ||
               lowerStr.includes("block") || lowerStr.includes("identify");
    }
    
    function logHttp(category, data) {
        var timestamp = new Date().toISOString();
        console.log("[HTTP_" + category + "] " + JSON.stringify(data, null, 2));
        
        if (category === "REQUEST") {
            httpRequests.push(Object.assign({}, data, {timestamp: timestamp}));
        } else if (category === "SOCKET") {
            socketConnections.push(Object.assign({}, data, {timestamp: timestamp}));
        } else if (category === "DNS") {
            dnsLookups.push(Object.assign({}, data, {timestamp: timestamp}));
        }
    }
    
    // ============= COMPREHENSIVE URL MONITORING =============
    try {
        var URL = Java.use("java.net.URL");
        
        // Hook all URL constructors
        URL.$init.overload('java.lang.String').implementation = function(spec) {
            if (spec && spec.startsWith("http") && !spec.includes("google") && 
                !spec.includes("firebase") && !spec.includes("youtube")) {
                
                logHttp("REQUEST", {
                    url: spec,
                    method: "URL.constructor",
                    isPhoneRelated: isPhoneRelated(spec),
                    type: "URL_CREATION"
                });
            }
            return this.$init(spec);
        };
        
        // Hook openConnection
        URL.openConnection.overload().implementation = function() {
            var connection = this.openConnection();
            var urlStr = this.toString();
            
            if (urlStr && !urlStr.includes("google") && !urlStr.includes("firebase") && 
                !urlStr.includes("youtube") && urlStr.startsWith("http")) {
                
                logHttp("REQUEST", {
                    url: urlStr,
                    method: "URL.openConnection",
                    isPhoneRelated: isPhoneRelated(urlStr),
                    type: "CONNECTION_OPEN"
                });
            }
            
            return connection;
        };
        
        console.log("[+] URL monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook URL: " + e.message);
    }
    
    // ============= SOCKET LEVEL MONITORING =============
    try {
        var Socket = Java.use("java.net.Socket");
        
        // Hook connect with SocketAddress
        Socket.connect.overload('java.net.SocketAddress').implementation = function(endpoint) {
            if (endpoint) {
                var endpointStr = endpoint.toString();
                if (!endpointStr.includes("127.0.0.1") && !endpointStr.includes("localhost")) {
                    logHttp("SOCKET", {
                        endpoint: endpointStr,
                        method: "Socket.connect",
                        isPhoneRelated: isPhoneRelated(endpointStr),
                        type: "SOCKET_CONNECTION"
                    });
                }
            }
            return this.connect(endpoint);
        };
        
        // Hook connect with timeout
        Socket.connect.overload('java.net.SocketAddress', 'int').implementation = function(endpoint, timeout) {
            if (endpoint) {
                var endpointStr = endpoint.toString();
                if (!endpointStr.includes("127.0.0.1") && !endpointStr.includes("localhost")) {
                    logHttp("SOCKET", {
                        endpoint: endpointStr,
                        timeout: timeout,
                        method: "Socket.connect_timeout",
                        isPhoneRelated: isPhoneRelated(endpointStr),
                        type: "SOCKET_CONNECTION"
                    });
                }
            }
            return this.connect(endpoint, timeout);
        };
        
        console.log("[+] Socket monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook Socket: " + e.message);
    }
    
    // ============= DNS RESOLUTION MONITORING =============
    try {
        var InetAddress = Java.use("java.net.InetAddress");
        
        // Hook getByName
        InetAddress.getByName.implementation = function(host) {
            var result = this.getByName(host);
            
            if (host && !host.includes("127.0.0.1") && !host.includes("localhost") &&
                !host.includes("google") && !host.includes("firebase")) {
                
                logHttp("DNS", {
                    hostname: host,
                    resolvedIp: result.getHostAddress(),
                    method: "InetAddress.getByName",
                    isPhoneRelated: isPhoneRelated(host),
                    type: "DNS_RESOLUTION"
                });
            }
            
            return result;
        };
        
        // Hook getAllByName
        InetAddress.getAllByName.implementation = function(host) {
            var results = this.getAllByName(host);
            
            if (host && !host.includes("127.0.0.1") && !host.includes("localhost") &&
                !host.includes("google") && !host.includes("firebase")) {
                
                var ips = [];
                for (var i = 0; i < results.length; i++) {
                    ips.push(results[i].getHostAddress());
                }
                
                logHttp("DNS", {
                    hostname: host,
                    resolvedIps: ips,
                    method: "InetAddress.getAllByName",
                    isPhoneRelated: isPhoneRelated(host),
                    type: "DNS_RESOLUTION"
                });
            }
            
            return results;
        };
        
        console.log("[+] DNS monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook DNS: " + e.message);
    }
    
    // ============= HTTP URL CONNECTION DEEP MONITORING =============
    try {
        var HttpURLConnection = Java.use("java.net.HttpURLConnection");
        
        // Hook getResponseCode to catch actual HTTP requests
        HttpURLConnection.getResponseCode.implementation = function() {
            var responseCode = this.getResponseCode();
            var url = this.getURL();
            
            if (url) {
                var urlStr = url.toString();
                if (!urlStr.includes("google") && !urlStr.includes("firebase") && 
                    !urlStr.includes("youtube")) {
                    
                    logHttp("REQUEST", {
                        url: urlStr,
                        responseCode: responseCode,
                        method: this.getRequestMethod(),
                        contentType: this.getContentType(),
                        userAgent: this.getRequestProperty("User-Agent"),
                        isPhoneRelated: isPhoneRelated(urlStr),
                        type: "HTTP_RESPONSE"
                    });
                }
            }
            
            return responseCode;
        };
        
        // Hook getInputStream to catch response reading
        HttpURLConnection.getInputStream.implementation = function() {
            var inputStream = this.getInputStream();
            var url = this.getURL();
            
            if (url) {
                var urlStr = url.toString();
                if (!urlStr.includes("google") && !urlStr.includes("firebase") && 
                    !urlStr.includes("youtube")) {
                    
                    logHttp("REQUEST", {
                        url: urlStr,
                        method: this.getRequestMethod(),
                        contentLength: this.getContentLength(),
                        isPhoneRelated: isPhoneRelated(urlStr),
                        type: "HTTP_RESPONSE_READ"
                    });
                }
            }
            
            return inputStream;
        };
        
        console.log("[+] HttpURLConnection deep monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook HttpURLConnection: " + e.message);
    }
    
    // ============= NATIVE LIBRARY MONITORING =============
    try {
        var System = Java.use("java.lang.System");
        
        // Hook loadLibrary to see what native libraries are loaded
        System.loadLibrary.implementation = function(libname) {
            if (libname && (libname.includes("http") || libname.includes("curl") || 
                           libname.includes("net") || libname.includes("flutter"))) {
                console.log("[NATIVE_LIB] Loading: " + libname);
            }
            return this.loadLibrary(libname);
        };
        
        console.log("[+] Native library monitoring active");
    } catch (e) {
        console.log("[-] Failed to hook System: " + e.message);
    }
    
    // ============= PERIODIC REPORTING =============
    var reportCount = 0;
    var reportInterval = setInterval(function() {
        reportCount++;
        
        console.log("\n" + "=".repeat(70));
        console.log("DEEP HTTP MONITOR REPORT #" + reportCount);
        console.log("=".repeat(70));
        
        if (httpRequests.length > 0) {
            console.log("\n[HTTP REQUESTS] (" + httpRequests.length + " total):");
            httpRequests.slice(-3).forEach(function(req, index) {
                console.log("  " + (index + 1) + ". " + req.url);
                console.log("     Type: " + req.type);
                console.log("     Method: " + (req.method || "Unknown"));
                console.log("     Phone Related: " + req.isPhoneRelated);
                console.log("");
            });
        }
        
        if (socketConnections.length > 0) {
            console.log("\n[SOCKET CONNECTIONS] (" + socketConnections.length + " total):");
            socketConnections.slice(-3).forEach(function(conn, index) {
                console.log("  " + (index + 1) + ". " + conn.endpoint);
                console.log("     Phone Related: " + conn.isPhoneRelated);
                console.log("");
            });
        }
        
        if (dnsLookups.length > 0) {
            console.log("\n[DNS LOOKUPS] (" + dnsLookups.length + " total):");
            dnsLookups.slice(-3).forEach(function(dns, index) {
                console.log("  " + (index + 1) + ". " + dns.hostname + " -> " + 
                           (dns.resolvedIp || dns.resolvedIps));
                console.log("     Phone Related: " + dns.isPhoneRelated);
                console.log("");
            });
        }
        
        if (httpRequests.length === 0 && socketConnections.length === 0) {
            console.log("\n[NO HTTP ACTIVITY DETECTED]");
            console.log("The app might be using:");
            console.log("- Native libraries for HTTP requests");
            console.log("- WebView-only communication");
            console.log("- Encrypted/obfuscated protocols");
        }
        
        console.log("=".repeat(70));
        
        // Stop after 8 reports
        if (reportCount >= 8) {
            clearInterval(reportInterval);
            console.log("[+] Deep HTTP monitoring completed");
        }
        
    }, 12000); // Report every 12 seconds
    
    console.log("[+] Deep HTTP Monitor active");
    console.log("[+] Monitoring: URLs, Sockets, DNS, Native libs");
    console.log("[+] Will report every 12 seconds");
});
