# Advanced .so Analysis with Radare2

## Phase 1: Basic Setup

### 1. Install Radare2
```bash
# Ubuntu/Debian
sudo apt install radare2

# Or build from source
git clone https://github.com/radareorg/radare2
cd radare2
./configure && make && sudo make install
```

### 2. Load and Analyze
```bash
# Open the library
r2 libapp.so

# Auto-analyze
[0x00000000]> aaa

# List functions
[0x00000000]> afl

# List strings
[0x00000000]> iz
```

## Phase 2: Advanced Radare2 Commands

### 1. String Analysis
```bash
# Find strings containing "phone"
[0x00000000]> iz~phone

# Find strings containing "api"
[0x00000000]> iz~api

# Find HTTP URLs
[0x00000000]> iz~http

# Show string cross-references
[0x00000000]> axt @@ str.*
```

### 2. Function Analysis
```bash
# List all functions
[0x00000000]> afl

# Find functions with "phone" in name
[0x00000000]> afl~phone

# Disassemble function
[0x00000000]> pdf @function_name

# Show function graph
[0x00000000]> VV @function_name
```

### 3. Cross-Reference Analysis
```bash
# Find cross-references to address
[0x00000000]> axt @address

# Find cross-references from address
[0x00000000]> axf @address

# Show all cross-references
[0x00000000]> ax
```

### 4. Search Operations
```bash
# Search for hex patterns
[0x00000000]> /x 48656c6c6f  # Search for "Hello"

# Search for strings
[0x00000000]> / string_to_find

# Search for API patterns
[0x00000000]> /c api
[0x00000000]> /c http
[0x00000000]> /c phone
```

## Phase 3: Radare2 Scripting

### 1. R2pipe Python Integration
```python
import r2pipe

# Open binary
r2 = r2pipe.open("libapp.so")

# Analyze
r2.cmd("aaa")

# Get all strings
strings = r2.cmd("iz")
print(strings)

# Find phone-related functions
functions = r2.cmd("afl~phone")
print(functions)
```

### 2. Automated Analysis Script
```bash
#!/bin/bash
# analyze_so.sh

echo "Analyzing $1..."
r2 -q -c "
aaa;
echo 'Functions:';
afl~phone;
echo 'Strings:';
iz~phone;
iz~api;
iz~http;
echo 'Cross-refs:';
axt @@ str.*~phone
" $1
```
