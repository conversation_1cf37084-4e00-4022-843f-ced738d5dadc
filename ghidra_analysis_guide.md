# Advanced .so File Analysis with <PERSON><PERSON>dra

## Phase 1: Initial Setup and Import

### 1. Create New Project
```
File -> New Project -> Non-Shared Project
Name: "SpamKiller_Analysis"
```

### 2. Import .so Files
```
File -> Import File
Select: libapp.so, libflutter.so, libtoolChecker.so (x86_64 versions)
Format: ELF (auto-detected)
Language: x86:LE:64:default (for x86_64)
```

### 3. Auto-Analysis
```
Analysis -> Auto Analyze
Enable all options, especially:
- Function ID
- Decompiler Parameter ID
- String Analysis
- Reference Analysis
```

## Phase 2: What to Look For

### 1. Phone/API Related Strings
```
Search -> For Strings
Filter by:
- "phone"
- "caller"
- "lookup"
- "api"
- "http"
- "contact"
- "spam"
- "block"
```

### 2. Network-Related Functions
```
Search -> For Functions
Look for:
- curl_*
- http_*
- socket
- connect
- send
- recv
- SSL_*
- TLS_*
```

### 3. Exported Functions
```
Window -> Symbol Tree
Expand "Functions"
Look for:
- JNI functions (Java_*)
- Flutter engine functions
- Custom API functions
```

## Phase 3: Deep Analysis Targets

### 1. libapp.so Analysis
**Primary Target**: This contains the main app logic

**Key Areas to Examine:**
```
1. JNI Bridge Functions:
   - Java_*_nativeMethod*
   - Look for phone number processing
   - API endpoint construction

2. String References:
   - URLs and endpoints
   - API keys or tokens
   - Phone number patterns

3. Network Functions:
   - HTTP request builders
   - Response parsers
   - Authentication mechanisms
```

### 2. libtoolChecker.so Analysis
**Suspicious Target**: This might be anti-analysis or security

**Key Areas:**
```
1. Anti-Debug Functions:
   - ptrace detection
   - debugger detection
   - emulator detection

2. Obfuscation:
   - String decryption
   - Control flow obfuscation
   - API hiding mechanisms

3. Security Checks:
   - Root detection
   - Integrity verification
   - Environment validation
```

### 3. libflutter.so Analysis
**Framework Target**: Standard Flutter engine

**Focus Areas:**
```
1. Dart VM Integration:
   - Native method bridges
   - Platform channels
   - Message passing

2. HTTP Client Implementation:
   - Dart HTTP to native bridges
   - Custom network implementations
```

## Phase 4: Specific Search Strategies

### 1. Find API Endpoints
```
Search Strategy:
1. Search for "http://" and "https://"
2. Look for domain patterns (.com, .net, .org)
3. Search for "api", "endpoint", "url"
4. Check for base64 encoded strings
5. Look for string concatenation functions
```

### 2. Find Phone Processing Logic
```
Search Strategy:
1. Search for regex patterns: "+", "(\d{3})", etc.
2. Look for "format", "validate", "normalize"
3. Search for country codes: "+1", "+44", etc.
4. Find number parsing functions
```

### 3. Find Encryption/Obfuscation
```
Search Strategy:
1. Look for crypto functions: AES, DES, RSA
2. Search for "encrypt", "decrypt", "hash"
3. Find base64 encode/decode
4. Look for XOR operations
5. Check for custom encoding schemes
```

## Phase 5: Advanced Techniques

### 1. Cross-Reference Analysis
```
Right-click on interesting strings/functions
-> References -> Find References to [symbol]
This shows where the function/string is used
```

### 2. Control Flow Analysis
```
Select function -> Window -> Function Graph
Analyze:
- Decision points
- Loop structures
- Error handling paths
- API call sequences
```

### 3. Data Type Recovery
```
Right-click variables -> Retype Variable
Define custom structures for:
- API request/response objects
- Phone number structures
- Configuration objects
```

## Phase 6: Scripting and Automation

### 1. Ghidra Scripts for Automation
```python
# Example: Find all HTTP-related strings
from ghidra.program.model.data import StringDataType

def find_http_strings():
    strings = []
    for string in currentProgram.getListing().getDefinedData(True):
        if isinstance(string.getDataType(), StringDataType):
            value = string.getValue()
            if "http" in str(value).lower():
                strings.append((string.getAddress(), value))
    return strings
```

### 2. Export Findings
```
File -> Export Program
Format: C/C++ (for function signatures)
Or use Ghidra's built-in reporting tools
```

## Phase 7: Dynamic Analysis Integration

### 1. Prepare for Frida Integration
```
1. Note interesting function addresses
2. Identify JNI function names
3. Map out API call flows
4. Document string decryption routines
```

### 2. Create Targeted Frida Scripts
```javascript
// Example: Hook discovered functions
var libapp = Module.findBaseAddress("libapp.so");
var target_func = libapp.add(0x12345); // Address from Ghidra

Interceptor.attach(target_func, {
    onEnter: function(args) {
        console.log("Function called with args:", args);
    }
});
```
