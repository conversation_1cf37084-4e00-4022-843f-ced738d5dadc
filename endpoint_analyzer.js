// Endpoint Analyzer - Analyzes captured transport data to extract meaningful endpoints
// Run this after collecting data with the enhanced transport monitor

Java.perform(function() {
    console.log("[+] Starting endpoint analysis...");
    
    var endpoints = [];
    var apiKeys = [];
    var domains = new Set();
    var phoneRelatedUrls = [];
    
    // ============= ANALYZE CURRENT NETWORK ACTIVITY =============
    try {
        // Hook URL constructor to catch any new URLs being created
        var URL = Java.use("java.net.URL");
        var originalInit = URL.$init.overload('java.lang.String');
        
        URL.$init.overload('java.lang.String').implementation = function(spec) {
            if (spec && spec.startsWith("http")) {
                endpoints.push({
                    url: spec,
                    timestamp: new Date().toISOString(),
                    source: "URL.constructor"
                });
                
                // Extract domain
                try {
                    var urlObj = originalInit.call(URL.$new(), spec);
                    domains.add(urlObj.getHost());
                } catch (e) {}
                
                // Check if phone related
                if (spec.toLowerCase().includes("phone") || 
                    spec.toLowerCase().includes("lookup") ||
                    spec.toLowerCase().includes("contact") ||
                    spec.toLowerCase().includes("caller")) {
                    phoneRelatedUrls.push(spec);
                }
                
                console.log("[ENDPOINT] " + spec);
            }
            
            return originalInit.call(this, spec);
        };
        
        console.log("[+] URL constructor analysis active");
    } catch (e) {
        console.log("[-] Failed to hook URL constructor: " + e.message);
    }
    
    // ============= ANALYZE HTTP CONNECTIONS =============
    try {
        var HttpURLConnection = Java.use("java.net.HttpURLConnection");
        
        // Override connect method to capture connection attempts
        HttpURLConnection.connect.implementation = function() {
            var url = this.getURL();
            if (url) {
                var urlStr = url.toString();
                console.log("[CONNECTION] Connecting to: " + urlStr);
                
                endpoints.push({
                    url: urlStr,
                    method: this.getRequestMethod(),
                    timestamp: new Date().toISOString(),
                    source: "HttpURLConnection.connect"
                });
            }
            
            return this.connect();
        };
        
        console.log("[+] HTTP connection analysis active");
    } catch (e) {
        console.log("[-] Failed to hook HTTP connections: " + e.message);
    }
    
    // ============= ANALYZE WEBVIEW URLS =============
    try {
        var WebView = Java.use("android.webkit.WebView");
        
        // Hook loadUrl to capture WebView URLs
        WebView.loadUrl.overload('java.lang.String').implementation = function(url) {
            if (url && url.startsWith("http")) {
                console.log("[WEBVIEW] Loading URL: " + url);
                
                endpoints.push({
                    url: url,
                    timestamp: new Date().toISOString(),
                    source: "WebView.loadUrl"
                });
                
                if (url.toLowerCase().includes("phone") || 
                    url.toLowerCase().includes("lookup")) {
                    phoneRelatedUrls.push(url);
                }
            }
            
            return this.loadUrl(url);
        };
        
        console.log("[+] WebView URL analysis active");
    } catch (e) {
        console.log("[-] Failed to hook WebView: " + e.message);
    }
    
    // ============= ANALYZE INTENT DATA =============
    try {
        var Intent = Java.use("android.content.Intent");
        
        // Hook setData to capture Intent URLs
        Intent.setData.implementation = function(data) {
            if (data) {
                var dataStr = data.toString();
                if (dataStr.startsWith("http")) {
                    console.log("[INTENT] Intent data: " + dataStr);
                    
                    endpoints.push({
                        url: dataStr,
                        timestamp: new Date().toISOString(),
                        source: "Intent.setData"
                    });
                }
            }
            
            return this.setData(data);
        };
        
        console.log("[+] Intent data analysis active");
    } catch (e) {
        console.log("[-] Failed to hook Intent: " + e.message);
    }
    
    // ============= PERIODIC REPORTING =============
    var reportInterval = setInterval(function() {
        if (endpoints.length > 0 || domains.size > 0) {
            console.log("\n" + "=".repeat(60));
            console.log("ENDPOINT ANALYSIS REPORT");
            console.log("=".repeat(60));
            
            console.log("\n[UNIQUE DOMAINS DETECTED]:");
            domains.forEach(function(domain) {
                console.log("  • " + domain);
            });
            
            console.log("\n[ALL ENDPOINTS CAPTURED]:");
            endpoints.forEach(function(endpoint, index) {
                console.log("  " + (index + 1) + ". " + endpoint.url);
                console.log("     Source: " + endpoint.source);
                console.log("     Time: " + endpoint.timestamp);
                if (endpoint.method) {
                    console.log("     Method: " + endpoint.method);
                }
                console.log("");
            });
            
            if (phoneRelatedUrls.length > 0) {
                console.log("\n[PHONE-RELATED ENDPOINTS]:");
                phoneRelatedUrls.forEach(function(url, index) {
                    console.log("  " + (index + 1) + ". " + url);
                });
            }
            
            if (apiKeys.length > 0) {
                console.log("\n[API KEYS DETECTED]:");
                apiKeys.forEach(function(key, index) {
                    console.log("  " + (index + 1) + ". " + key.substring(0, 30) + "...");
                });
            }
            
            console.log("\n" + "=".repeat(60));
        }
    }, 15000); // Report every 15 seconds
    
    // ============= CLEANUP =============
    setTimeout(function() {
        clearInterval(reportInterval);
        console.log("[+] Endpoint analysis completed");
    }, 120000); // Stop after 2 minutes
    
    console.log("[+] Endpoint analyzer running for 2 minutes...");
    console.log("[+] Will report findings every 15 seconds");
});
