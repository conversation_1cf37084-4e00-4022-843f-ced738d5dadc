// Safe API Monitor - Compatible script that won't crash the app
// Focuses on capturing API calls without interfering with Flutter initialization

Java.perform(function() {
    console.log("[+] Safe API Monitor starting...");
    
    var apiCalls = [];
    var webviewActivity = [];
    var networkActivity = [];
    
    function isPhoneRelated(str) {
        if (!str) return false;
        var lowerStr = str.toLowerCase();
        return lowerStr.includes("phone") || lowerStr.includes("caller") ||
               lowerStr.includes("lookup") || lowerStr.includes("contact") ||
               lowerStr.includes("number") || lowerStr.includes("search") ||
               lowerStr.includes("api") || lowerStr.includes("spam") ||
               lowerStr.includes("block") || lowerStr.includes("identify") ||
               lowerStr.includes("truecaller") || lowerStr.includes("getcontact");
    }
    
    function safeLog(category, data) {
        try {
            var timestamp = new Date().toISOString();
            console.log("[" + category + "] " + JSON.stringify(data, null, 2));
            
            var entry = {
                category: category,
                data: data,
                timestamp: timestamp,
                isPhoneRelated: isPhoneRelated(JSON.stringify(data))
            };
            
            if (category === "API") {
                apiCalls.push(entry);
            } else if (category === "WEBVIEW") {
                webviewActivity.push(entry);
            } else if (category === "NETWORK") {
                networkActivity.push(entry);
            }
        } catch (e) {
            console.log("[-] Logging error: " + e.message);
        }
    }
    
    // ============= SAFE WEBVIEW MONITORING =============
    setTimeout(function() {
        try {
            var WebView = Java.use("android.webkit.WebView");
            
            // Hook loadUrl safely
            WebView.loadUrl.overload('java.lang.String').implementation = function(url) {
                try {
                    if (url && url.startsWith("http") && !url.includes("data:")) {
                        safeLog("WEBVIEW", {
                            action: "loadUrl",
                            url: url,
                            method: "WebView.loadUrl"
                        });
                    }
                } catch (e) {
                    console.log("[-] WebView hook error: " + e.message);
                }
                return this.loadUrl(url);
            };
            
            console.log("[+] WebView monitoring active");
        } catch (e) {
            console.log("[-] Failed to hook WebView: " + e.message);
        }
    }, 3000); // Wait 3 seconds for app to initialize
    
    // ============= SAFE URL MONITORING =============
    setTimeout(function() {
        try {
            var URL = Java.use("java.net.URL");
            
            URL.$init.overload('java.lang.String').implementation = function(spec) {
                try {
                    if (spec && spec.startsWith("http") && 
                        !spec.includes("google") && !spec.includes("firebase") && 
                        !spec.includes("youtube") && !spec.includes("gstatic")) {
                        
                        safeLog("NETWORK", {
                            action: "url_creation",
                            url: spec,
                            method: "URL.constructor"
                        });
                    }
                } catch (e) {
                    console.log("[-] URL hook error: " + e.message);
                }
                return this.$init(spec);
            };
            
            console.log("[+] URL monitoring active");
        } catch (e) {
            console.log("[-] Failed to hook URL: " + e.message);
        }
    }, 2000); // Wait 2 seconds
    
    // ============= SAFE HTTP CONNECTION MONITORING =============
    setTimeout(function() {
        try {
            var HttpURLConnection = Java.use("java.net.HttpURLConnection");
            
            HttpURLConnection.getResponseCode.implementation = function() {
                var responseCode = this.getResponseCode();
                try {
                    var url = this.getURL();
                    if (url) {
                        var urlStr = url.toString();
                        if (!urlStr.includes("google") && !urlStr.includes("firebase") && 
                            !urlStr.includes("youtube") && !urlStr.includes("gstatic")) {
                            
                            safeLog("API", {
                                action: "http_response",
                                url: urlStr,
                                responseCode: responseCode,
                                method: this.getRequestMethod(),
                                contentType: this.getContentType()
                            });
                        }
                    }
                } catch (e) {
                    console.log("[-] HTTP response hook error: " + e.message);
                }
                return responseCode;
            };
            
            console.log("[+] HTTP monitoring active");
        } catch (e) {
            console.log("[-] Failed to hook HTTP: " + e.message);
        }
    }, 4000); // Wait 4 seconds
    
    // ============= SAFE INTENT MONITORING =============
    setTimeout(function() {
        try {
            var Intent = Java.use("android.content.Intent");
            
            Intent.setData.implementation = function(data) {
                try {
                    if (data && data.toString().startsWith("http")) {
                        safeLog("NETWORK", {
                            action: "intent_data",
                            data: data.toString(),
                            method: "Intent.setData"
                        });
                    }
                } catch (e) {
                    console.log("[-] Intent hook error: " + e.message);
                }
                return this.setData(data);
            };
            
            console.log("[+] Intent monitoring active");
        } catch (e) {
            console.log("[-] Failed to hook Intent: " + e.message);
        }
    }, 1000); // Wait 1 second
    
    // ============= SAFE REPORTING =============
    var reportCount = 0;
    var reportInterval = setInterval(function() {
        try {
            reportCount++;
            
            console.log("\n" + "=".repeat(60));
            console.log("SAFE API MONITOR REPORT #" + reportCount);
            console.log("=".repeat(60));
            
            var phoneRelatedItems = [];
            var allItems = apiCalls.concat(webviewActivity).concat(networkActivity);
            
            for (var i = 0; i < allItems.length; i++) {
                if (allItems[i].isPhoneRelated) {
                    phoneRelatedItems.push(allItems[i]);
                }
            }
            
            if (apiCalls.length > 0) {
                console.log("\n[API CALLS] (" + apiCalls.length + " total):");
                var recentApi = apiCalls.slice(-3);
                for (var i = 0; i < recentApi.length; i++) {
                    var item = recentApi[i];
                    console.log("  " + (i + 1) + ". " + item.data.url);
                    console.log("     Action: " + item.data.action);
                    console.log("     Method: " + (item.data.method || "Unknown"));
                    console.log("     Phone Related: " + item.isPhoneRelated);
                    console.log("");
                }
            }
            
            if (webviewActivity.length > 0) {
                console.log("\n[WEBVIEW ACTIVITY] (" + webviewActivity.length + " total):");
                var recentWebview = webviewActivity.slice(-3);
                for (var i = 0; i < recentWebview.length; i++) {
                    var item = recentWebview[i];
                    console.log("  " + (i + 1) + ". " + item.data.url);
                    console.log("     Action: " + item.data.action);
                    console.log("     Phone Related: " + item.isPhoneRelated);
                    console.log("");
                }
            }
            
            if (networkActivity.length > 0) {
                console.log("\n[NETWORK ACTIVITY] (" + networkActivity.length + " total):");
                var recentNetwork = networkActivity.slice(-3);
                for (var i = 0; i < recentNetwork.length; i++) {
                    var item = recentNetwork[i];
                    console.log("  " + (i + 1) + ". " + (item.data.url || item.data.data));
                    console.log("     Action: " + item.data.action);
                    console.log("     Phone Related: " + item.isPhoneRelated);
                    console.log("");
                }
            }
            
            if (phoneRelatedItems.length > 0) {
                console.log("\n[PHONE-RELATED ACTIVITY] (" + phoneRelatedItems.length + " total):");
                var recentPhone = phoneRelatedItems.slice(-5);
                for (var i = 0; i < recentPhone.length; i++) {
                    var item = recentPhone[i];
                    console.log("  " + (i + 1) + ". [" + item.category + "] " + 
                               (item.data.url || item.data.data || "Unknown"));
                    console.log("     Action: " + item.data.action);
                    console.log("");
                }
            }
            
            if (apiCalls.length === 0 && webviewActivity.length === 0 && networkActivity.length === 0) {
                console.log("\n[NO ACTIVITY DETECTED YET]");
                console.log("Try using the app's phone lookup features:");
                console.log("- Search for phone numbers");
                console.log("- Use caller ID functionality");
                console.log("- Check spam blocking features");
                console.log("- Navigate through different app screens");
            }
            
            console.log("=".repeat(60));
            
            // Stop after 12 reports (2 minutes)
            if (reportCount >= 12) {
                clearInterval(reportInterval);
                console.log("[+] Safe API monitoring completed");
                
                // Final summary
                console.log("\n[FINAL SUMMARY]");
                console.log("Total API calls: " + apiCalls.length);
                console.log("Total WebView activity: " + webviewActivity.length);
                console.log("Total network activity: " + networkActivity.length);
                console.log("Phone-related items: " + phoneRelatedItems.length);
            }
            
        } catch (e) {
            console.log("[-] Report error: " + e.message);
        }
    }, 10000); // Report every 10 seconds
    
    console.log("[+] Safe API Monitor initialized");
    console.log("[+] Waiting for app to fully load before hooking...");
    console.log("[+] Will start monitoring in 1-4 seconds");
    console.log("[+] Reports every 10 seconds for 2 minutes");
});
