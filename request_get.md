# Request.get:
package com.google.android.gms.internal.consent_sdk;

import android.util.Log;
import android.webkit.WebResourceRequest;
import android.webkit.WebView;
import android.webkit.WebViewClient;

/* loaded from: classes.dex */
final class zzbv extends WebViewClient {
    final /* synthetic */ zzbw zza;

    /* synthetic */ zzbv(zzbw zzbwVar, zzbu zzbuVar) {
        this.zza = zzbwVar;
    }

    @Override // android.webkit.WebViewClient
    public final void onLoadResource(WebView webView, String str) {
        if (zzbw.zze(this.zza, str)) {
            this.zza.zzb.zzd(str);
        }
    }

    @Override // android.webkit.WebViewClient
    public final void onPageFinished(WebView webView, String str) {
        if (this.zza.zzc) {
            return;
        }
        Log.d("UserMessagingPlatform", "Wall html loaded.");
        this.zza.zzc = true;
    }

    @Override // android.webkit.WebViewClient
    public final void onReceivedError(WebView webView, int i10, String str, String str2) {
        this.zza.zzb.zze(i10, str, str2);
    }

    @Override // android.webkit.WebViewClient
    public final boolean shouldOverrideUrlLoading(WebView webView, WebResourceRequest webResourceRequest) {
        String string = webResourceRequest.getUrl().toString();
        if (!zzbw.zze(this.zza, string)) {
            return false;
        }
        this.zza.zzb.zzd(string);
        return true;
    }

    @Override // android.webkit.WebViewClient
    public final boolean shouldOverrideUrlLoading(WebView webView, String str) {
        if (!zzbw.zze(this.zza, str)) {
            return false;
        }
        this.zza.zzb.zzd(str);
        return true;
    }
}

# com.google.firebase.perf.network.FirebasePerfHttpClient
package com.google.firebase.perf.network;

import a9.k;
import androidx.annotation.Keep;
import b9.l;
import java.io.IOException;
import org.apache.http.HttpHost;
import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.protocol.HttpContext;
import w8.i;
import y8.e;
import y8.g;

/* loaded from: classes2.dex */
public class FirebasePerfHttpClient {
    private FirebasePerfHttpClient() {
    }

    static <T> T a(HttpClient httpClient, HttpHost httpHost, HttpRequest httpRequest, ResponseHandler<? extends T> responseHandler, l lVar, k kVar) throws IOException {
        i iVarI = i.i(kVar);
        try {
            iVarI.C(httpHost.toURI() + httpRequest.getRequestLine().getUri()).r(httpRequest.getRequestLine().getMethod());
            Long lA = g.a(httpRequest);
            if (lA != null) {
                iVarI.v(lA.longValue());
            }
            lVar.h();
            iVarI.w(lVar.e());
            return (T) httpClient.execute(httpHost, httpRequest, new e(responseHandler, lVar, iVarI));
        } catch (IOException e10) {
            iVarI.A(lVar.c());
            g.d(iVarI);
            throw e10;
        }
    }

    static <T> T b(HttpClient httpClient, HttpHost httpHost, HttpRequest httpRequest, ResponseHandler<? extends T> responseHandler, HttpContext httpContext, l lVar, k kVar) throws IOException {
        i iVarI = i.i(kVar);
        try {
            iVarI.C(httpHost.toURI() + httpRequest.getRequestLine().getUri()).r(httpRequest.getRequestLine().getMethod());
            Long lA = g.a(httpRequest);
            if (lA != null) {
                iVarI.v(lA.longValue());
            }
            lVar.h();
            iVarI.w(lVar.e());
            return (T) httpClient.execute(httpHost, httpRequest, new e(responseHandler, lVar, iVarI), httpContext);
        } catch (IOException e10) {
            iVarI.A(lVar.c());
            g.d(iVarI);
            throw e10;
        }
    }

    static <T> T c(HttpClient httpClient, HttpUriRequest httpUriRequest, ResponseHandler<T> responseHandler, l lVar, k kVar) throws IOException {
        i iVarI = i.i(kVar);
        try {
            iVarI.C(httpUriRequest.getURI().toString()).r(httpUriRequest.getMethod());
            Long lA = g.a(httpUriRequest);
            if (lA != null) {
                iVarI.v(lA.longValue());
            }
            lVar.h();
            iVarI.w(lVar.e());
            return (T) httpClient.execute(httpUriRequest, new e(responseHandler, lVar, iVarI));
        } catch (IOException e10) {
            iVarI.A(lVar.c());
            g.d(iVarI);
            throw e10;
        }
    }

    static <T> T d(HttpClient httpClient, HttpUriRequest httpUriRequest, ResponseHandler<T> responseHandler, HttpContext httpContext, l lVar, k kVar) throws IOException {
        i iVarI = i.i(kVar);
        try {
            iVarI.C(httpUriRequest.getURI().toString()).r(httpUriRequest.getMethod());
            Long lA = g.a(httpUriRequest);
            if (lA != null) {
                iVarI.v(lA.longValue());
            }
            lVar.h();
            iVarI.w(lVar.e());
            return (T) httpClient.execute(httpUriRequest, new e(responseHandler, lVar, iVarI), httpContext);
        } catch (IOException e10) {
            iVarI.A(lVar.c());
            g.d(iVarI);
            throw e10;
        }
    }

    static HttpResponse e(HttpClient httpClient, HttpHost httpHost, HttpRequest httpRequest, l lVar, k kVar) throws IOException {
        i iVarI = i.i(kVar);
        try {
            iVarI.C(httpHost.toURI() + httpRequest.getRequestLine().getUri()).r(httpRequest.getRequestLine().getMethod());
            Long lA = g.a(httpRequest);
            if (lA != null) {
                iVarI.v(lA.longValue());
            }
            lVar.h();
            iVarI.w(lVar.e());
            HttpResponse httpResponseExecute = httpClient.execute(httpHost, httpRequest);
            iVarI.A(lVar.c());
            iVarI.s(httpResponseExecute.getStatusLine().getStatusCode());
            Long lA2 = g.a(httpResponseExecute);
            if (lA2 != null) {
                iVarI.y(lA2.longValue());
            }
            String strB = g.b(httpResponseExecute);
            if (strB != null) {
                iVarI.x(strB);
            }
            iVarI.h();
            return httpResponseExecute;
        } catch (IOException e10) {
            iVarI.A(lVar.c());
            g.d(iVarI);
            throw e10;
        }
    }

    @Keep
    public static <T> T execute(HttpClient httpClient, HttpHost httpHost, HttpRequest httpRequest, ResponseHandler<? extends T> responseHandler) {
        return (T) a(httpClient, httpHost, httpRequest, responseHandler, new l(), k.l());
    }

    @Keep
    public static <T> T execute(HttpClient httpClient, HttpHost httpHost, HttpRequest httpRequest, ResponseHandler<? extends T> responseHandler, HttpContext httpContext) {
        return (T) b(httpClient, httpHost, httpRequest, responseHandler, httpContext, new l(), k.l());
    }

    @Keep
    public static <T> T execute(HttpClient httpClient, HttpUriRequest httpUriRequest, ResponseHandler<T> responseHandler) {
        return (T) c(httpClient, httpUriRequest, responseHandler, new l(), k.l());
    }

    @Keep
    public static <T> T execute(HttpClient httpClient, HttpUriRequest httpUriRequest, ResponseHandler<T> responseHandler, HttpContext httpContext) {
        return (T) d(httpClient, httpUriRequest, responseHandler, httpContext, new l(), k.l());
    }

    @Keep
    public static HttpResponse execute(HttpClient httpClient, HttpHost httpHost, HttpRequest httpRequest) {
        return e(httpClient, httpHost, httpRequest, new l(), k.l());
    }

    @Keep
    public static HttpResponse execute(HttpClient httpClient, HttpHost httpHost, HttpRequest httpRequest, HttpContext httpContext) {
        return f(httpClient, httpHost, httpRequest, httpContext, new l(), k.l());
    }

    @Keep
    public static HttpResponse execute(HttpClient httpClient, HttpUriRequest httpUriRequest) {
        return g(httpClient, httpUriRequest, new l(), k.l());
    }

    @Keep
    public static HttpResponse execute(HttpClient httpClient, HttpUriRequest httpUriRequest, HttpContext httpContext) {
        return h(httpClient, httpUriRequest, httpContext, new l(), k.l());
    }

    static HttpResponse f(HttpClient httpClient, HttpHost httpHost, HttpRequest httpRequest, HttpContext httpContext, l lVar, k kVar) throws IOException {
        i iVarI = i.i(kVar);
        try {
            iVarI.C(httpHost.toURI() + httpRequest.getRequestLine().getUri()).r(httpRequest.getRequestLine().getMethod());
            Long lA = g.a(httpRequest);
            if (lA != null) {
                iVarI.v(lA.longValue());
            }
            lVar.h();
            iVarI.w(lVar.e());
            HttpResponse httpResponseExecute = httpClient.execute(httpHost, httpRequest, httpContext);
            iVarI.A(lVar.c());
            iVarI.s(httpResponseExecute.getStatusLine().getStatusCode());
            Long lA2 = g.a(httpResponseExecute);
            if (lA2 != null) {
                iVarI.y(lA2.longValue());
            }
            String strB = g.b(httpResponseExecute);
            if (strB != null) {
                iVarI.x(strB);
            }
            iVarI.h();
            return httpResponseExecute;
        } catch (IOException e10) {
            iVarI.A(lVar.c());
            g.d(iVarI);
            throw e10;
        }
    }

    static HttpResponse g(HttpClient httpClient, HttpUriRequest httpUriRequest, l lVar, k kVar) throws IOException {
        i iVarI = i.i(kVar);
        try {
            iVarI.C(httpUriRequest.getURI().toString()).r(httpUriRequest.getMethod());
            Long lA = g.a(httpUriRequest);
            if (lA != null) {
                iVarI.v(lA.longValue());
            }
            lVar.h();
            iVarI.w(lVar.e());
            HttpResponse httpResponseExecute = httpClient.execute(httpUriRequest);
            iVarI.A(lVar.c());
            iVarI.s(httpResponseExecute.getStatusLine().getStatusCode());
            Long lA2 = g.a(httpResponseExecute);
            if (lA2 != null) {
                iVarI.y(lA2.longValue());
            }
            String strB = g.b(httpResponseExecute);
            if (strB != null) {
                iVarI.x(strB);
            }
            iVarI.h();
            return httpResponseExecute;
        } catch (IOException e10) {
            iVarI.A(lVar.c());
            g.d(iVarI);
            throw e10;
        }
    }

    static HttpResponse h(HttpClient httpClient, HttpUriRequest httpUriRequest, HttpContext httpContext, l lVar, k kVar) throws IOException {
        i iVarI = i.i(kVar);
        try {
            iVarI.C(httpUriRequest.getURI().toString()).r(httpUriRequest.getMethod());
            Long lA = g.a(httpUriRequest);
            if (lA != null) {
                iVarI.v(lA.longValue());
            }
            lVar.h();
            iVarI.w(lVar.e());
            HttpResponse httpResponseExecute = httpClient.execute(httpUriRequest, httpContext);
            iVarI.A(lVar.c());
            iVarI.s(httpResponseExecute.getStatusLine().getStatusCode());
            Long lA2 = g.a(httpResponseExecute);
            if (lA2 != null) {
                iVarI.y(lA2.longValue());
            }
            String strB = g.b(httpResponseExecute);
            if (strB != null) {
                iVarI.x(strB);
            }
            iVarI.h();
            return httpResponseExecute;
        } catch (IOException e10) {
            iVarI.A(lVar.c());
            g.d(iVarI);
            throw e10;
        }
    }
}