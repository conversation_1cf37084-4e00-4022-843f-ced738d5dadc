// Transport System Monitor - Target Google's CCT transport used to hide real API calls
// Based on static analysis revealing hidden phone lookup infrastructure

var logFile = null;

function initTransportLogging() {
    try {
        logFile = new File("/data/data/app.menodag.spamkiller/transport_system.log", "w");
        console.log("[+] Transport system monitor log initialized");
    } catch (e) {
        try {
            logFile = new File("/sdcard/transport_system.log", "w");
            console.log("[+] Transport system monitor log initialized: /sdcard/");
        } catch (e2) {
            console.log("[-] Failed to initialize transport system log: " + e2.message);
        }
    }
}

function logTransport(category, details) {
    var timestamp = new Date().toISOString();
    var logEntry = timestamp + " [TRANSPORT_" + category + "] " + JSON.stringify(details) + "\n";
    
    console.log("[TRANSPORT] " + category + ": " + JSON.stringify(details, null, 2));
    
    if (logFile) {
        try {
            logFile.write(logEntry);
            logFile.flush();
        } catch (e) {}
    }
}

function decodeBase64(str) {
    try {
        var Base64 = Java.use("android.util.Base64");
        var decoded = Base64.decode(str, 0);
        return Java.use("java.lang.String").$new(decoded, "UTF-8");
    } catch (e) {
        return "Failed to decode: " + e.message;
    }
}

function isPhoneRelated(str) {
    if (!str) return false;
    var lowerStr = str.toLowerCase();
    return lowerStr.includes("phone") || 
           lowerStr.includes("caller") ||
           lowerStr.includes("lookup") ||
           lowerStr.includes("zain") ||
           lowerStr.includes("zoneb") ||
           lowerStr.includes("msisdn") ||
           lowerStr.includes("subscriber") ||
           lowerStr.includes("contact") ||
           lowerStr.includes("getcontact") ||
           lowerStr.includes("outbrain") ||
           lowerStr.includes("name") ||
           lowerStr.includes("number");
}

Java.perform(function() {
    initTransportLogging();
    
    // ============= TRANSPORT CONTEXT CONFIGURATION =============
    try {
        // Hook the transport context creation from data_transport.md analysis
        var ContentValues = Java.use("android.content.ContentValues");
        
        ContentValues.put.overload('java.lang.String', 'java.lang.String').implementation = function(key, value) {
            // Monitor transport context configuration
            if (key && (key === "backend_name" || key === "extras" || key === "transport_name")) {
                var decodedValue = value;
                
                if (key === "extras" && value) {
                    decodedValue = decodeBase64(value);
                }
                
                if (isPhoneRelated(key) || isPhoneRelated(value) || isPhoneRelated(decodedValue)) {
                    logTransport("CONFIG", {
                        key: key,
                        value: value,
                        decodedValue: key === "extras" ? decodedValue : null,
                        method: "ContentValues.put"
                    });
                }
            }
            
            return this.put(key, value);
        };
        
        console.log("[+] Hooked ContentValues for transport configuration");
    } catch (e) {
        console.log("[-] Failed to hook ContentValues: " + e.message);
    }
    
    // ============= HTTP URL CONNECTION WITH API KEYS =============
    try {
        var HttpURLConnection = Java.use("java.net.HttpURLConnection");
        
        // Hook setRequestProperty to catch X-Goog-Api-Key and other headers
        HttpURLConnection.setRequestProperty.implementation = function(key, value) {
            if (key && (key.includes("Api-Key") || key.includes("Authorization") || 
                       key.includes("X-Goog") || isPhoneRelated(key) || isPhoneRelated(value))) {
                
                logTransport("HTTP_HEADER", {
                    header: key,
                    value: value,
                    url: this.getURL() ? this.getURL().toString() : null,
                    method: "HttpURLConnection.setRequestProperty"
                });
            }
            
            return this.setRequestProperty(key, value);
        };
        
        console.log("[+] Hooked HttpURLConnection for API key monitoring");
    } catch (e) {
        console.log("[-] Failed to hook HttpURLConnection: " + e.message);
    }
    
    // ============= DYNAMIC URL CONFIGURATION =============
    try {
        var URL = Java.use("java.net.URL");
        
        // Hook URL constructor to see dynamic URLs being created
        URL.$init.overload('java.lang.String').implementation = function(spec) {
            if (spec && !spec.includes("google") && !spec.includes("firebase") && 
                !spec.includes("gstatic") && spec.includes("http")) {
                
                logTransport("DYNAMIC_URL", {
                    url: spec,
                    isPhoneRelated: isPhoneRelated(spec),
                    method: "URL.init"
                });
            }
            
            return this.$init(spec);
        };
        
        console.log("[+] Hooked URL for dynamic URL monitoring");
    } catch (e) {
        console.log("[-] Failed to hook URL: " + e.message);
    }
    
    // ============= TRANSPORT EVENT PAYLOADS =============
    try {
        // Monitor event payloads that might contain phone lookup data
        var SQLiteDatabase = Java.use("android.database.sqlite.SQLiteDatabase");
        
        SQLiteDatabase.insert.implementation = function(table, nullColumnHack, values) {
            var result = this.insert(table, nullColumnHack, values);
            
            if (table === "events" || table === "event_payloads") {
                try {
                    var payload = values.get("payload");
                    var transportName = values.get("transport_name");
                    var eventId = values.get("event_id");
                    
                    if (payload || transportName || eventId) {
                        var payloadStr = "";
                        if (payload) {
                            try {
                                payloadStr = Java.use("java.lang.String").$new(payload, "UTF-8");
                            } catch (e) {
                                payloadStr = "Binary payload, length: " + payload.length;
                            }
                        }
                        
                        if (isPhoneRelated(transportName) || isPhoneRelated(payloadStr)) {
                            logTransport("EVENT_PAYLOAD", {
                                table: table,
                                transportName: transportName,
                                eventId: eventId,
                                payload: payloadStr.substring(0, 500) + (payloadStr.length > 500 ? "..." : ""),
                                method: "SQLiteDatabase.insert"
                            });
                        }
                    }
                } catch (e) {}
            }
            
            return result;
        };
        
        console.log("[+] Hooked SQLiteDatabase for event payload monitoring");
    } catch (e) {
        console.log("[-] Failed to hook SQLiteDatabase: " + e.message);
    }
    
    // ============= WEBVIEW JAVASCRIPT INTERFACE =============
    try {
        var WebView = Java.use("android.webkit.WebView");
        
        // Hook evaluateJavascript to see what JavaScript is being executed
        WebView.evaluateJavascript.implementation = function(script, resultCallback) {
            if (script && (isPhoneRelated(script) || 
                          script.includes("fetch") || 
                          script.includes("XMLHttpRequest") ||
                          script.includes("ajax"))) {
                
                logTransport("WEBVIEW_JS", {
                    script: script.substring(0, 500) + (script.length > 500 ? "..." : ""),
                    isPhoneRelated: isPhoneRelated(script),
                    method: "WebView.evaluateJavascript"
                });
            }
            
            return this.evaluateJavascript(script, resultCallback);
        };
        
        console.log("[+] Hooked WebView for JavaScript monitoring");
    } catch (e) {
        console.log("[-] Failed to hook WebView: " + e.message);
    }
    
    // ============= BASE64 DECODING MONITORING =============
    try {
        var Base64 = Java.use("android.util.Base64");
        
        // Hook Base64 decode to see what's being decoded
        Base64.decode.overload('[B', 'int').implementation = function(input, flags) {
            var result = this.decode(input, flags);
            
            try {
                var inputStr = Java.use("java.lang.String").$new(input, "UTF-8");
                var resultStr = Java.use("java.lang.String").$new(result, "UTF-8");
                
                if (isPhoneRelated(inputStr) || isPhoneRelated(resultStr)) {
                    logTransport("BASE64_DECODE", {
                        input: inputStr.substring(0, 200),
                        output: resultStr.substring(0, 200),
                        method: "Base64.decode"
                    });
                }
            } catch (e) {}
            
            return result;
        };
        
        console.log("[+] Hooked Base64 for decoding monitoring");
    } catch (e) {
        console.log("[-] Failed to hook Base64: " + e.message);
    }
    
    // ============= GZIP STREAM MONITORING =============
    try {
        var GZIPOutputStream = Java.use("java.util.zip.GZIPOutputStream");
        
        // Hook write to see what's being compressed and sent
        GZIPOutputStream.write.overload('[B', 'int', 'int').implementation = function(buf, off, len) {
            try {
                var data = Java.array('byte', buf.slice(off, off + len));
                var dataStr = Java.use("java.lang.String").$new(data, "UTF-8");
                
                if (isPhoneRelated(dataStr)) {
                    logTransport("GZIP_DATA", {
                        data: dataStr.substring(0, 300),
                        length: len,
                        method: "GZIPOutputStream.write"
                    });
                }
            } catch (e) {}
            
            return this.write(buf, off, len);
        };
        
        console.log("[+] Hooked GZIPOutputStream for compressed data monitoring");
    } catch (e) {
        console.log("[-] Failed to hook GZIPOutputStream: " + e.message);
    }
    
    console.log("[+] Transport system monitoring active");
    console.log("[+] Targeting: CCT transport, dynamic URLs, API keys, event payloads, WebView JS");
    console.log("[+] Looking for: Phone lookup data hidden in Google's transport infrastructure");
});
