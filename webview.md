# io.flutter.plugins.urllauncher.WebViewActivity
package io.flutter.plugins.urllauncher;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Message;
import android.view.KeyEvent;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/* loaded from: classes2.dex */
public class WebViewActivity extends Activity {

    /* renamed from: c, reason: collision with root package name */
    WebView f13024c;

    /* renamed from: a, reason: collision with root package name */
    private final BroadcastReceiver f13022a = new a();

    /* renamed from: b, reason: collision with root package name */
    private final WebViewClient f13023b = new b();

    /* renamed from: d, reason: collision with root package name */
    private final IntentFilter f13025d = new IntentFilter("close action");

    class a extends BroadcastReceiver {
        a() {
        }

        @Override // android.content.BroadcastReceiver
        public void onReceive(Context context, Intent intent) {
            if ("close action".equals(intent.getAction())) {
                WebViewActivity.this.finish();
            }
        }
    }

    class b extends WebViewClient {
        b() {
        }

        @Override // android.webkit.WebViewClient
        public boolean shouldOverrideUrlLoading(WebView webView, WebResourceRequest webResourceRequest) {
            webView.loadUrl(webResourceRequest.getUrl().toString());
            return false;
        }

        @Override // android.webkit.WebViewClient
        public boolean shouldOverrideUrlLoading(WebView webView, String str) {
            return super.shouldOverrideUrlLoading(webView, str);
        }
    }

    class c extends WebChromeClient {

        class a extends WebViewClient {
            a() {
            }

            @Override // android.webkit.WebViewClient
            public boolean shouldOverrideUrlLoading(WebView webView, WebResourceRequest webResourceRequest) {
                WebViewActivity.this.f13024c.loadUrl(webResourceRequest.getUrl().toString());
                return true;
            }

            @Override // android.webkit.WebViewClient
            public boolean shouldOverrideUrlLoading(WebView webView, String str) {
                WebViewActivity.this.f13024c.loadUrl(str);
                return true;
            }
        }

        c() {
        }

        @Override // android.webkit.WebChromeClient
        public boolean onCreateWindow(WebView webView, boolean z10, boolean z11, Message message) {
            a aVar = new a();
            WebView webView2 = new WebView(WebViewActivity.this.f13024c.getContext());
            webView2.setWebViewClient(aVar);
            ((WebView.WebViewTransport) message.obj).setWebView(webView2);
            message.sendToTarget();
            return true;
        }
    }

    public static Intent a(Context context, String str, boolean z10, boolean z11, Bundle bundle) {
        return new Intent(context, (Class<?>) WebViewActivity.class).putExtra("url", str).putExtra("enableJavaScript", z10).putExtra("enableDomStorage", z11).putExtra("com.android.browser.headers", bundle);
    }

    public static Map<String, String> b(Bundle bundle) {
        if (bundle == null) {
            return Collections.emptyMap();
        }
        HashMap map = new HashMap();
        for (String str : bundle.keySet()) {
            map.put(str, bundle.getString(str));
        }
        return map;
    }

    @Override // android.app.Activity
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        WebView webView = new WebView(this);
        this.f13024c = webView;
        setContentView(webView);
        Intent intent = getIntent();
        String stringExtra = intent.getStringExtra("url");
        boolean booleanExtra = intent.getBooleanExtra("enableJavaScript", false);
        boolean booleanExtra2 = intent.getBooleanExtra("enableDomStorage", false);
        this.f13024c.loadUrl(stringExtra, b(intent.getBundleExtra("com.android.browser.headers")));
        this.f13024c.getSettings().setJavaScriptEnabled(booleanExtra);
        this.f13024c.getSettings().setDomStorageEnabled(booleanExtra2);
        this.f13024c.setWebViewClient(this.f13023b);
        this.f13024c.getSettings().setSupportMultipleWindows(true);
        this.f13024c.setWebChromeClient(new c());
        androidx.core.content.a.registerReceiver(this, this.f13022a, this.f13025d, 2);
    }

    @Override // android.app.Activity
    protected void onDestroy() {
        super.onDestroy();
        unregisterReceiver(this.f13022a);
    }

    @Override // android.app.Activity, android.view.KeyEvent.Callback
    public boolean onKeyDown(int i10, KeyEvent keyEvent) {
        if (i10 != 4 || !this.f13024c.canGoBack()) {
            return super.onKeyDown(i10, keyEvent);
        }
        this.f13024c.goBack();
        return true;
    }
}


# io.flutter.plugins.webviewflutter.e4
package io.flutter.plugins.webviewflutter;

import android.view.View;
import android.webkit.ConsoleMessage;
import android.webkit.GeolocationPermissions;
import android.webkit.PermissionRequest;
import android.webkit.WebChromeClient;
import android.webkit.WebView;
import io.flutter.plugins.webviewflutter.n;
import java.util.List;
import java.util.Objects;

/* loaded from: classes2.dex */
public class e4 extends n.w {

    /* renamed from: b, reason: collision with root package name */
    private final ta.c f13058b;

    /* renamed from: c, reason: collision with root package name */
    private final n3 f13059c;

    /* renamed from: d, reason: collision with root package name */
    private final q5 f13060d;

    static /* synthetic */ class a {

        /* renamed from: a, reason: collision with root package name */
        static final /* synthetic */ int[] f13061a;

        static {
            int[] iArr = new int[ConsoleMessage.MessageLevel.values().length];
            f13061a = iArr;
            try {
                iArr[ConsoleMessage.MessageLevel.TIP.ordinal()] = 1;
            } catch (NoSuchFieldError unused) {
            }
            try {
                f13061a[ConsoleMessage.MessageLevel.LOG.ordinal()] = 2;
            } catch (NoSuchFieldError unused2) {
            }
            try {
                f13061a[ConsoleMessage.MessageLevel.WARNING.ordinal()] = 3;
            } catch (NoSuchFieldError unused3) {
            }
            try {
                f13061a[ConsoleMessage.MessageLevel.ERROR.ordinal()] = 4;
            } catch (NoSuchFieldError unused4) {
            }
            try {
                f13061a[ConsoleMessage.MessageLevel.DEBUG.ordinal()] = 5;
            } catch (NoSuchFieldError unused5) {
            }
        }
    }

    public e4(ta.c cVar, n3 n3Var) {
        super(cVar);
        this.f13058b = cVar;
        this.f13059c = n3Var;
        this.f13060d = new q5(cVar, n3Var);
    }

    private long G(WebChromeClient webChromeClient) {
        Long lH = this.f13059c.h(webChromeClient);
        if (lH != null) {
            return lH.longValue();
        }
        throw new IllegalStateException("Could not find identifier for WebChromeClient.");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void H(Void r02) {
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void I(Void r02) {
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void J(Void r02) {
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void K(Void r02) {
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void L(Void r02) {
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void M(Void r02) {
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void N(Void r02) {
    }

    private static n.b W(ConsoleMessage.MessageLevel messageLevel) {
        int i10 = a.f13061a[messageLevel.ordinal()];
        return i10 != 1 ? i10 != 2 ? i10 != 3 ? i10 != 4 ? i10 != 5 ? n.b.UNKNOWN : n.b.DEBUG : n.b.ERROR : n.b.WARNING : n.b.LOG : n.b.TIP;
    }

    public void O(WebChromeClient webChromeClient, ConsoleMessage consoleMessage, n.w.a<Void> aVar) {
        Long lH = this.f13059c.h(webChromeClient);
        Objects.requireNonNull(lH);
        super.r(lH, new n.a.C0200a().c(Long.valueOf(consoleMessage.lineNumber())).d(consoleMessage.message()).b(W(consoleMessage.messageLevel())).e(consoleMessage.sourceId()).a(), aVar);
    }

    public void P(WebChromeClient webChromeClient, n.w.a<Void> aVar) {
        Long lH = this.f13059c.h(webChromeClient);
        Objects.requireNonNull(lH);
        super.s(lH, aVar);
    }

    public void Q(WebChromeClient webChromeClient, String str, GeolocationPermissions.Callback callback, n.w.a<Void> aVar) {
        new k3(this.f13058b, this.f13059c).a(callback, new n.l.a() { // from class: io.flutter.plugins.webviewflutter.z3
            @Override // io.flutter.plugins.webviewflutter.n.l.a
            public final void a(Object obj) {
                e4.H((Void) obj);
            }
        });
        Long lH = this.f13059c.h(webChromeClient);
        Objects.requireNonNull(lH);
        Long lH2 = this.f13059c.h(callback);
        Objects.requireNonNull(lH2);
        t(lH, lH2, str, aVar);
    }

    public void R(WebChromeClient webChromeClient, n.w.a<Void> aVar) {
        Long lH = this.f13059c.h(webChromeClient);
        Objects.requireNonNull(lH);
        super.u(lH, aVar);
    }

    public void S(WebChromeClient webChromeClient, PermissionRequest permissionRequest, n.w.a<Void> aVar) {
        new u3(this.f13058b, this.f13059c).a(permissionRequest, permissionRequest.getResources(), new n.s.a() { // from class: io.flutter.plugins.webviewflutter.d4
            @Override // io.flutter.plugins.webviewflutter.n.s.a
            public final void a(Object obj) {
                e4.I((Void) obj);
            }
        });
        Long lH = this.f13059c.h(webChromeClient);
        Objects.requireNonNull(lH);
        Long lH2 = this.f13059c.h(permissionRequest);
        Objects.requireNonNull(lH2);
        super.v(lH, lH2, aVar);
    }

    public void T(WebChromeClient webChromeClient, WebView webView, Long l10, n.w.a<Void> aVar) {
        this.f13060d.a(webView, new n.g0.a() { // from class: io.flutter.plugins.webviewflutter.a4
            @Override // io.flutter.plugins.webviewflutter.n.g0.a
            public final void a(Object obj) {
                e4.J((Void) obj);
            }
        });
        Long lH = this.f13059c.h(webView);
        Objects.requireNonNull(lH);
        super.w(Long.valueOf(G(webChromeClient)), lH, l10, aVar);
    }

    public void U(WebChromeClient webChromeClient, View view, WebChromeClient.CustomViewCallback customViewCallback, n.w.a<Void> aVar) {
        new w3(this.f13058b, this.f13059c).a(view, new n.v.a() { // from class: io.flutter.plugins.webviewflutter.x3
            @Override // io.flutter.plugins.webviewflutter.n.v.a
            public final void a(Object obj) {
                e4.K((Void) obj);
            }
        });
        new d(this.f13058b, this.f13059c).a(customViewCallback, new n.d.a() { // from class: io.flutter.plugins.webviewflutter.y3
            @Override // io.flutter.plugins.webviewflutter.n.d.a
            public final void a(Object obj) {
                e4.L((Void) obj);
            }
        });
        Long lH = this.f13059c.h(webChromeClient);
        Objects.requireNonNull(lH);
        Long lH2 = this.f13059c.h(view);
        Objects.requireNonNull(lH2);
        Long lH3 = this.f13059c.h(customViewCallback);
        Objects.requireNonNull(lH3);
        x(lH, lH2, lH3, aVar);
    }

    public void V(WebChromeClient webChromeClient, WebView webView, WebChromeClient.FileChooserParams fileChooserParams, n.w.a<List<String>> aVar) {
        this.f13060d.a(webView, new n.g0.a() { // from class: io.flutter.plugins.webviewflutter.b4
            @Override // io.flutter.plugins.webviewflutter.n.g0.a
            public final void a(Object obj) {
                e4.M((Void) obj);
            }
        });
        new j(this.f13058b, this.f13059c).e(fileChooserParams, new n.i.a() { // from class: io.flutter.plugins.webviewflutter.c4
            @Override // io.flutter.plugins.webviewflutter.n.i.a
            public final void a(Object obj) {
                e4.N((Void) obj);
            }
        });
        Long lH = this.f13059c.h(webChromeClient);
        Objects.requireNonNull(lH);
        Long lH2 = this.f13059c.h(webView);
        Objects.requireNonNull(lH2);
        Long lH3 = this.f13059c.h(fileChooserParams);
        Objects.requireNonNull(lH3);
        y(lH, lH2, lH3, aVar);
    }
}